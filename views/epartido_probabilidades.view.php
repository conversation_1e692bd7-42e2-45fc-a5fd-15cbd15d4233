<?php
#region region DOCS
/** @var string $id_partido */
/** @var Partido $cur_partido */
/** @var Pais[] $paises */
/** @var array $paises_torneos_info */
/** @var PartidoTorneo[] $partido_torneos */
/** @var PartidoInfo[] $partidos_infos */
/** @var int $tabselected */
/** @var int $tabselected_corners_inferior */
/** @var int $n_partidos_primer_corte */
/** @var int $n_partidos_segundo_corte */
/** @var int $n_row_partidos_infos */
/** @var float $prob_total_goles_masde_1_5_p1_home_h */
/** @var float $prob_total_goles_masde_1_5_p2_home_h */
/** @var float $prob_total_goles_masde_1_5_p1_away_a */
/** @var float $prob_total_goles_masde_1_5_p2_away_a */
/** @var float $prob_total_goles_masde_1_5_p1_home */
/** @var float $prob_total_goles_masde_1_5_p2_home */
/** @var float $prob_total_goles_masde_1_5_p1_away */
/** @var float $prob_total_goles_masde_1_5_p2_away */
/** @var float $prob_total_corners_masde_6_5_p1_home_h */
/** @var float $prob_total_corners_masde_6_5_p2_home_h */
/** @var float $prob_total_corners_masde_6_5_p1_away_a */
/** @var float $prob_total_corners_masde_6_5_p2_away_a */
/** @var float $prob_total_corners_masde_6_5_p1_home */
/** @var float $prob_total_corners_masde_6_5_p2_home */
/** @var float $prob_total_corners_masde_6_5_p1_away */
/** @var float $prob_total_corners_masde_6_5_p2_away */
/** @var float $prob_home_marca_p1_home_h */
/** @var float $prob_home_marca_p2_home_h */
/** @var float $prob_home_marca_p1_home */
/** @var float $prob_home_marca_p2_home */
/** @var float $prob_home_superior_1_5_p1_home_h */
/** @var float $prob_home_superior_1_5_p2_home_h */
/** @var float $prob_home_superior_1_5_p1_home */
/** @var float $prob_home_superior_1_5_p2_home */
/** @var float $prob_away_marca_p1_away_a */
/** @var float $prob_away_marca_p2_away_a */
/** @var float $prob_away_marca_p1_away */
/** @var float $prob_away_marca_p2_away */
/** @var float $prob_away_superior_1_5_p1_away_a */
/** @var float $prob_away_superior_1_5_p2_away_a */
/** @var float $prob_away_superior_1_5_p1_away */
/** @var float $prob_away_superior_1_5_p2_away */
/** @var float $prob_ambos_marcan_p1_home_h */
/** @var float $prob_ambos_marcan_p2_home_h */
/** @var float $prob_ambos_marcan_p1_away_a */
/** @var float $prob_ambos_marcan_p2_away_a */
/** @var float $prob_ambos_marcan_p1_home */
/** @var float $prob_ambos_marcan_p2_home */
/** @var float $prob_ambos_marcan_p1_away */
/** @var float $prob_ambos_marcan_p2_away */
/** @var float $avg_goals_p1_home */
/** @var float $avg_goals_p2_home */
/** @var float $avg_goals_p1_away */
/** @var float $avg_goals_p2_away */
/** @var float $avg_conceded_p1_home */
/** @var float $avg_conceded_p2_home */
/** @var float $avg_conceded_p1_away */
/** @var float $avg_conceded_p2_away */
/** @var float $avg_corners_p1_home */
/** @var float $avg_corners_p2_home */
/** @var float $avg_corners_p1_away */
/** @var float $avg_corners_p2_away */
/** @var float $avg_total_corners_p1_home */
/** @var float $avg_total_corners_p2_home */
/** @var float $avg_total_corners_p1_away */
/** @var float $avg_total_corners_p2_away */
/** @var float $min_porc_viable */
/** @var int $numero_por_revisar */
/** @var PartidoTorneo[] $partidos_torneos_grouped */
#endregion DOCS
?>
<?php if (empty($tabselected)) { $tabselected = 1; } ?>
<?php if (empty($tabselected_corners_inferior)) { $tabselected_corners_inferior = 7; } ?>
<?php
// Check for existing betting records for visual indicators
$existing_bets = [];
if (!empty($id_partido)) {
    try {
        // Check for goles section - ALL bet type IDs
        $goles_bet_types = [48, 47, 49, 50, 51, 52];
        $goles_keys = ['total_superior_1_5', 'home_superior_0_5', 'home_superior_1_5', 'away_superior_0_5', 'away_superior_1_5', 'ambos_marcan'];

        for ($i = 0; $i < count($goles_bet_types); $i++) {
            $existing_bets[$goles_keys[$i]] = \App\classes\PartidoBetDetalle::existsActiveForMatchAndBetType(
                $id_partido,
                desordena($goles_bet_types[$i]),
                $conexion
            );
        }

        // Check for corners totales superior section (bet type IDs 53-60)
        $corners_superior_bet_types = [53, 54, 55, 56, 57, 58, 59, 60];
        $corners_superior_keys = ['corner_6_5', 'corner_7_5', 'corner_8_5', 'corner_9_5', 'corner_10_5', 'corner_11_5', 'corner_12_5', 'corner_13_5'];

        for ($i = 0; $i < count($corners_superior_bet_types); $i++) {
            $existing_bets[$corners_superior_keys[$i]] = \App\classes\PartidoBetDetalle::existsActiveForMatchAndBetType(
                $id_partido,
                desordena($corners_superior_bet_types[$i]),
                $conexion
            );
        }

        // Check for corners totales inferior section (bet type IDs 61-68)
        $corners_inferior_bet_types = [61, 62, 63, 64, 65, 66, 67, 68];
        $corners_inferior_keys = ['corner_inferior_6_5', 'corner_inferior_7_5', 'corner_inferior_8_5', 'corner_inferior_9_5', 'corner_inferior_10_5', 'corner_inferior_11_5', 'corner_inferior_12_5', 'corner_inferior_13_5'];

        for ($i = 0; $i < count($corners_inferior_bet_types); $i++) {
            $existing_bets[$corners_inferior_keys[$i]] = \App\classes\PartidoBetDetalle::existsActiveForMatchAndBetType(
                $id_partido,
                desordena($corners_inferior_bet_types[$i]),
                $conexion
            );
        }
    } catch (Exception $e) {
        // Log error but don't break the page
        error_log("Error checking existing bets: " . $e->getMessage());
        $existing_bets = [];
    }
}
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>My Dash | Probabilidades</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<link href="<?php echo RUTA ?>resources/assets/plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
	<?php #region region CSS grouped controls ?>
	<link href="<?php echo RUTA ?>resources/css/grouped_controls.css" rel="stylesheet" />
	<?php #endregion CSS grouped controls ?>
	
	<link href="<?php echo RUTA ?>resources/css/epartido_probabilidades.css" rel="stylesheet" />
	
	<style>
</style>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex justify-content-between align-items-center">
			<h4>Ver probabilidades</h4>
			<?php
			// Get the count of matches pending review
			$matches_pending_review = Partido::get_numero_por_revisar_probabilidades([], $conexion);
			if ($matches_pending_review > 0):
			?>
			<div class="badge bg-warning fs-14px">
				<i class="fas fa-clock me-1"></i>
				<?php echo $matches_pending_review; ?> matches pending review
			</div>
			<?php endif; ?>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FORM ?>
		<form action="epartido_probabilidades" method="POST">
			<input type="hidden" id="id_partido" name="id_partido" value="<?php echo @recover_var($id_partido) ?>">
			<input type="hidden" id="tabselected" name="tabselected" value="<?php echo limpiar_datos((isset($tabselected) && $tabselected) ? $tabselected : 1); ?>">
			<input type="hidden" id="tabselected_corners" name="tabselected_corners" value="<?php echo limpiar_datos((isset($tabselected_corners) && $tabselected_corners) ? $tabselected_corners : 7); ?>">
			<input type="hidden" id="tabselected_corners_inferior" name="tabselected_corners_inferior" value="<?php echo limpiar_datos((isset($tabselected_corners_inferior) && $tabselected_corners_inferior) ? $tabselected_corners_inferior : 7); ?>">

			<!-- BEGIN Match Header Card -->
			<div class="card match-header-card mt-3 shadow-lg">
				<div class="card-body p-4">
					<!-- Match Teams Row -->
					<div class="row align-items-center mb-3">
						<div class="col-md-5">
							<div class="text-center">
								<div class="badge bg-primary mb-2 fs-10px">HOME</div>
								<h4 class="text-white mb-0 fw-bold cursor-pointer team-name" id="home" onclick="copyWithToast(this.id)" title="Click to copy">
									<i class="fas fa-home me-2 text-primary"></i><?php echo @recover_var($cur_partido->home) ?>
								</h4>
								<!-- Tournament badges for home team -->
								<div class="mt-2" id="home-tournaments">
									<div class="d-flex justify-content-center flex-wrap gap-1" id="home-tournament-badges">
										<!-- Tournament badges will be loaded here -->
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-2">
							<div class="text-center">
								<div class="vs-circle rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
									<span class="text-white fw-bold fs-18px">VS</span>
								</div>
							</div>
						</div>
						<div class="col-md-5">
							<div class="text-center">
								<div class="badge bg-warning mb-2 fs-10px">AWAY</div>
								<h4 class="text-white mb-0 fw-bold cursor-pointer team-name" id="away" onclick="copyWithToast(this.id)" title="Click to copy">
									<?php echo @recover_var($cur_partido->away) ?><i class="fas fa-plane ms-2 text-warning"></i>
								</h4>
								<!-- Tournament badges for away team -->
								<div class="mt-2" id="away-tournaments">
									<div class="d-flex justify-content-center flex-wrap gap-1" id="away-tournament-badges">
										<!-- Tournament badges will be loaded here -->
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Match Details Row -->
					<div class="row">
						<div class="col-md-6 mb-3 mb-md-0">
							<div class="d-flex align-items-center match-detail-box rounded p-3">
								<i class="fas fa-trophy text-success me-3 fs-18px"></i>
								<div>
									<small class="text-muted d-block">Tournament</small>
									<span class="text-white fw-semibold cursor-pointer" id="torneo" onclick="copyWithToast(this.id)" title="Click to copy">
										<?php echo @recover_var($cur_partido->pais_torneo->nombre) ?>
									</span>
								</div>
							</div>
						</div>
						<div class="col-md-6">
							<div class="d-flex align-items-center match-detail-box rounded p-3">
								<i class="fas fa-calendar-alt text-info me-3 fs-18px"></i>
								<div class="flex-grow-1">
									<small class="text-muted d-block">Match Date & Time</small>
									<span class="text-white fw-semibold cursor-pointer" id="fecha_hora" onclick="copyWithToast(this.id)" title="Click to copy">
										<?php echo formatPartidoFechaHora($cur_partido); ?>
									</span>
									<!-- Countdown display -->
									<div class="mt-1" id="countdown_container" style="display: none;">
										<div class="badge bg-info countdown-badge fs-10px">
											<i class="fas fa-clock me-1"></i>
											<span id="countdown_text"></span>
										</div>
									</div>
									<!-- Match status badges -->
									<div class="mt-1" id="match_status_container" style="display: none;">
										<div id="match_status_badge" class="badge fs-10px">
											<i class="fas fa-play me-1"></i>
											<span id="match_status_text"></span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- END Match Header Card -->
			<!-- BEGIN Action Buttons Row -->
			<div class="row mt-3">
				<?php #region region LINK regresar ?>
				<div class="col-md-4 col-xs-12 mb-2">
					<a href="lpartidos" class="btn btn-outline-light btn-sm w-100 d-flex align-items-center justify-content-center">
						<i class="fas fa-list me-2"></i>Ir a listado de partidos
					</a>
				</div>
				<?php #endregion LINK regresar ?>
				<?php #region region LINK ir a listado de apuestas ?>
				<div class="col-md-4 col-xs-12 mb-2">
					<a href="partidos-bets" class="btn btn-outline-info btn-sm w-100 d-flex align-items-center justify-content-center">
						<i class="fas fa-list-alt me-2"></i>Ir a listado de apuestas
					</a>
				</div>
				<?php #endregion LINK ir a listado de apuestas ?>
				<?php #region region SUBMIT sub_refrescar ?>
				<div class="col-md-4 col-xs-12 mb-2">
					<button type="submit" id="sub_refrescar" name="sub_refrescar" class="btn btn-primary btn-sm w-100 d-flex align-items-center justify-content-center">
						<i class="fas fa-sync-alt me-2"></i>Refrescar
					</button>
				</div>
				<?php #endregion SUBMIT sub_refrescar ?>
			</div>
			<!-- END Action Buttons Row -->
			<?php #region region PANEL torneos ?>
			<div class="panel panel-inverse mt-3 no-border-radious">
				<div class="panel-heading no-border-radious">
					<div class="col-md-4 col-xs-12">
						<h4 class="panel-title">
							Torneos incluidos:
						</h4>
					</div>
					<!-- BEGIN buttons section -->
					<div class="col-md-8 col-xs-12">
						<div class="d-flex gap-2 justify-content-end">
							<button type="button" class="btn btn-xs btn-success" onclick="showCreateTournamentModal()">
								<i class="fas fa-plus me-1"></i>Create New
							</button>
							<button type="button" class="btn btn-xs btn-primary" onclick="showAddTournamentModal()">
								<i class="fas fa-plus-circle me-1"></i>Add Tournament
							</button>
						</div>

					</div>
					<!-- END buttons section -->
				</div>
				<!-- BEGIN PANEL body -->
				<div class="p-1 table-nowrap" style="overflow: auto">
					<?php #region region TABLE torneos ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="text-center">Acciones</th>
							<th class="text-center">Nombre</th>
							<th class="text-center">Temporada</th>
							<th class="text-center">ID Footy</th>
							<th class="text-center">Fecha actualizado</th>
						</tr>
						</thead>
						<tbody class="fs-12px">
						<?php foreach ($partidos_torneos_grouped as $partido_torneo): ?>
							<tr class="cursor-pointer">
								<td class="text-center align-middle">
									<div class="d-flex gap-1 justify-content-center">
										<button type="button" class="btn btn-xs btn-primary"
											onclick="openUploadModal('<?php echo $partido_torneo->pais->id; ?>', '<?php echo addslashes($partido_torneo->pais->nombre); ?>', '<?php echo $partido_torneo->season; ?>')"
											title="Cargar datos para este torneo">
											<i class="fas fa-upload me-1"></i>Cargar datos
										</button>
										<?php if (!empty($partido_torneo->footy_id)): ?>
										<button type="button" class="btn btn-xs btn-info"
											onclick="actualizarViaAPI('<?php echo $partido_torneo->pais->id; ?>', '<?php echo $partido_torneo->season; ?>', '<?php echo $partido_torneo->footy_id; ?>', '<?php echo addslashes($partido_torneo->pais->nombre); ?>')"
											title="Actualizar datos via API">
											<i class="fas fa-sync me-1"></i>Actualizar via API
										</button>
										<?php endif; ?>
										<button type="button" class="btn btn-xs btn-warning"
											onclick="openEditTournamentModal('<?php echo $partido_torneo->pais->id; ?>', '<?php echo addslashes($partido_torneo->pais->nombre); ?>')"
											title="Editar torneo">
											<i class="fas fa-edit me-1"></i>Edit
										</button>
									</div>
								</td>
								<td class="align-middle"><?php echo $partido_torneo->pais->nombre; ?></td>
								<td class="text-center align-middle"><?php echo $partido_torneo->season; ?></td>
								<td class="text-center align-middle">
									<?php if (!empty($partido_torneo->footy_id)): ?>
										<span class="badge bg-info fs-12px"><?php echo $partido_torneo->footy_id; ?></span>
									<?php else: ?>
										<span class="text-muted">-</span>
									<?php endif; ?>
								</td>
								<td class="text-center align-middle">
									<span class="<?php echo $partido_torneo->text_color_class; ?>">
										<?php echo $partido_torneo->fecha_upload; ?>
										<?php
										// Add days ago calculation with consistent coloring
										if (!empty($partido_torneo->fecha_upload) && $partido_torneo->days_diff !== null) {
											if ($partido_torneo->days_diff > 0) {
												echo ' <small>(' . $partido_torneo->days_diff . ' days ago)</small>';
											} elseif ($partido_torneo->days_diff == 0) {
												echo ' <small>(today)</small>';
											}
										}
										?>
									</span>
								</td>
							</tr>
						<?php endforeach; ?>
						</tbody>
					</table>
					<?php #endregion TABLE torneos ?>
				</div>
				<!-- END PANEL body -->
			</div>
			<?php #endregion PANEL torneos ?>

			<!-- BEGIN 'Marcar como revisado' Button Row (moved below torneos incluidos section) -->
			<div class="row mt-3">
				<?php #region region SUBMIT sub_marcar_revisado_probabilidades ?>
				<div class="col-md-12 col-xs-12">
					<button type="submit" id="sub_marcar_revisado_probabilidades_top" name="sub_marcar_revisado_probabilidades" class="btn btn-sm btn-success w-100 no-border-radious">
						Marcar como revisado
					</button>
				</div>
				<?php #endregion SUBMIT sub_marcar_revisado_probabilidades ?>
			</div>
			<!-- END 'Marcar como revisado' Button Row -->

			<h2>Goles:</h2>
			
			<?php #region region NAVTAB HEAD analisis ?>
			<ul class="region_NAVTAB_HEAD nav nav-tabs fs-13px mt-3 no-border-radious">
				<li class="nav-item" onclick="tabselect(1)">
					<a href="#default-tab-1" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 1) ? "active" : ""; ?>">
						Total SUPERIOR 1.5
						<span id="porc_acierto_1_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['total_superior_1_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(3)">
					<a href="#default-tab-3" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 3) ? "active" : ""; ?>">
						Home SUPERIOR 0.5
						<span id="porc_acierto_home_marca_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['home_superior_0_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(20)">
					<a href="#default-tab-20" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 20) ? "active" : ""; ?>">
						Home SUPERIOR 1.5
						<span id="porc_acierto_home_superior_1_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['home_superior_1_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(4)">
					<a href="#default-tab-4" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 4) ? "active" : ""; ?>">
						Away SUPERIOR 0.5
						<span id="porc_acierto_away_marca_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['away_superior_0_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(21)">
					<a href="#default-tab-21" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 21) ? "active" : ""; ?>">
						Away SUPERIOR 1.5
						<span id="porc_acierto_away_superior_1_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['away_superior_1_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(5)">
					<a href="#default-tab-5" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 5) ? "active" : ""; ?>">
						Ambos marcan
						<span id="porc_acierto_ambos_marcan_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['ambos_marcan'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
			</ul>
			<div class="tab-content panel rounded-0 rounded-bottom">
				<?php #region region TAB total_goles_masde_1_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected == 1) ? "active show" : ""; ?>" id="default-tab-1">
					<?php #region region TABLE total_goles_masde_1_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="8" class="text-center">5 partidos</th>
							<th colspan="8" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center home-criteria-header">Home @H</th>
							<th class="text-center home-criteria-header">Home</th>
							<th class="text-center home-criteria-header">Avg Goals<br>Home</th>
							<th class="text-center home-criteria-header">Avg Conceded<br>Home</th>
							<th class="text-center away-criteria-header">Away @A</th>
							<th class="text-center away-criteria-header">Away</th>
							<th class="text-center away-criteria-header">Avg Goals<br>Away</th>
							<th class="text-center away-criteria-header">Avg Conceded<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home @H</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Goals<br>Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Conceded<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away @A</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Goals<br>Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Conceded<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_1_5"><?php echo $prob_total_goles_masde_1_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_1_5"><?php echo $prob_total_goles_masde_1_5_p1_home; ?>%</td>
							<td class="text-center value_num_1_5"><?php echo number_format($avg_goals_p1_home, 2); ?></td>
							<td class="text-center value_num_1_5"><?php echo number_format($avg_conceded_p1_home, 2); ?></td>
							<td class="text-center value_porc_1_5"><?php echo $prob_total_goles_masde_1_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_1_5"><?php echo $prob_total_goles_masde_1_5_p1_away; ?>%</td>
							<td class="text-center value_num_1_5"><?php echo number_format($avg_goals_p1_away, 2); ?></td>
							<td class="text-center value_num_1_5"><?php echo number_format($avg_conceded_p1_away, 2); ?></td>
							<td class="text-center value_porc_1_5 bg-gray-900"><?php echo $prob_total_goles_masde_1_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_1_5 bg-gray-900"><?php echo $prob_total_goles_masde_1_5_p2_home; ?>%</td>
							<td class="text-center value_num_1_5 bg-gray-900"><?php echo number_format($avg_goals_p2_home, 2); ?></td>
							<td class="text-center value_num_1_5 bg-gray-900"><?php echo number_format($avg_conceded_p2_home, 2); ?></td>
							<td class="text-center value_porc_1_5 bg-gray-900"><?php echo $prob_total_goles_masde_1_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_1_5 bg-gray-900"><?php echo $prob_total_goles_masde_1_5_p2_away; ?>%</td>
							<td class="text-center value_num_1_5 bg-gray-900"><?php echo number_format($avg_goals_p2_away, 2); ?></td>
							<td class="text-center value_num_1_5 bg-gray-900"><?php echo number_format($avg_conceded_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table total_goles_masde_1_5 ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_1_5" name="porc_acierto_1_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_total_goles_masde_1_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_prob_total_goles_masde_1_5" name="sub_agregar_prob_total_goles_masde_1_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('total_superior_1_5', 48)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_total_goles_masde_1_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB total_goles_masde_1_5 ?>
				<?php #region region TAB home_marca ?>
				<div class="tab-pane fade <?php echo ($tabselected == 3) ? "active show" : ""; ?>" id="default-tab-3">
					<?php #region region TABLE home_marca ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="4" class="text-center">5 partidos</th>
							<th colspan="4" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center home-criteria-header">Home @H</th>
							<th class="text-center home-criteria-header">Home</th>
							<th class="text-center home-criteria-header">Avg Goals<br>Home</th>
							<th class="text-center away-criteria-header">Avg Conceded<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home @H</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Goals<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Conceded<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_home_marca"><?php echo $prob_home_marca_p1_home_h; ?>%</td>
							<td class="text-center value_porc_home_marca"><?php echo $prob_home_marca_p1_home; ?>%</td>
							<td class="text-center value_num_home_marca"><?php echo number_format($avg_goals_p1_home, 2); ?></td>
							<td class="text-center value_num_home_marca"><?php echo number_format($avg_conceded_p1_away, 2); ?></td>
							<td class="text-center value_porc_home_marca bg-gray-900"><?php echo $prob_home_marca_p2_home_h; ?>%</td>
							<td class="text-center value_porc_home_marca bg-gray-900"><?php echo $prob_home_marca_p2_home; ?>%</td>
							<td class="text-center value_num_home_marca bg-gray-900"><?php echo number_format($avg_goals_p2_home, 2); ?></td>
							<td class="text-center value_num_home_marca bg-gray-900"><?php echo number_format($avg_conceded_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table home_marca ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_home_marca" name="porc_acierto_home_marca" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_home_marca ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_prob_home_marca" name="sub_agregar_prob_home_marca" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('home_superior_0_5', 47)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_home_marca ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB home_marca ?>
				<?php #region region TAB home_superior_1_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected == 20) ? "active show" : ""; ?>" id="default-tab-20">
					<?php #region region TABLE home_superior_1_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="4" class="text-center">5 partidos</th>
							<th colspan="4" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center home-criteria-header">Home @H</th>
							<th class="text-center home-criteria-header">Home</th>
							<th class="text-center home-criteria-header">Avg Goals<br>Home</th>
							<th class="text-center away-criteria-header">Avg Conceded<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home @H</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Goals<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Conceded<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_home_superior_1_5"><?php echo $prob_home_superior_1_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_home_superior_1_5"><?php echo $prob_home_superior_1_5_p1_home; ?>%</td>
							<td class="text-center value_num_home_superior_1_5"><?php echo number_format($avg_goals_p1_home, 2); ?></td>
							<td class="text-center value_num_home_conceded_superior_1_5"><?php echo number_format($avg_conceded_p1_away, 2); ?></td>
							<td class="text-center value_porc_home_superior_1_5 bg-gray-900"><?php echo $prob_home_superior_1_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_home_superior_1_5 bg-gray-900"><?php echo $prob_home_superior_1_5_p2_home; ?>%</td>
							<td class="text-center value_num_home_superior_1_5 bg-gray-900"><?php echo number_format($avg_goals_p2_home, 2); ?></td>
							<td class="text-center value_num_home_conceded_superior_1_5 bg-gray-900"><?php echo number_format($avg_conceded_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table home_superior_1_5 ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_home_superior_1_5" name="porc_acierto_home_superior_1_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_home_superior_1_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_prob_home_superior_1_5" name="sub_agregar_prob_home_superior_1_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('home_superior_1_5', 49)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_home_superior_1_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB home_superior_1_5 ?>
				<?php #region region TAB away_marca ?>
				<div class="tab-pane fade <?php echo ($tabselected == 4) ? "active show" : ""; ?>" id="default-tab-4">
					<?php #region region TABLE away_marca ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="4" class="text-center">5 partidos</th>
							<th colspan="4" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center away-criteria-header">Away @A</th>
							<th class="text-center away-criteria-header">Away</th>
							<th class="text-center away-criteria-header">Avg Goals<br>Away</th>
							<th class="text-center home-criteria-header">Avg Conceded<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away @A</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Goals<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Conceded<br>Home</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_away_marca"><?php echo $prob_away_marca_p1_away_a; ?>%</td>
							<td class="text-center value_porc_away_marca"><?php echo $prob_away_marca_p1_away; ?>%</td>
							<td class="text-center value_num_away_marca"><?php echo number_format($avg_goals_p1_away, 2); ?></td>
							<td class="text-center value_num_away_marca"><?php echo number_format($avg_conceded_p1_home, 2); ?></td>
							<td class="text-center value_porc_away_marca bg-gray-900"><?php echo $prob_away_marca_p2_away_a; ?>%</td>
							<td class="text-center value_porc_away_marca bg-gray-900"><?php echo $prob_away_marca_p2_away; ?>%</td>
							<td class="text-center value_num_away_marca bg-gray-900"><?php echo number_format($avg_goals_p2_away, 2); ?></td>
							<td class="text-center value_num_away_marca bg-gray-900"><?php echo number_format($avg_conceded_p2_home, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table away_marca ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_away_marca" name="porc_acierto_away_marca" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_away_marca ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_prob_away_marca" name="sub_agregar_prob_away_marca" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('away_superior_0_5', 50)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_away_marca ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB away_marca ?>
				<?php #region region TAB away_superior_1_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected == 21) ? "active show" : ""; ?>" id="default-tab-21">
					<?php #region region TABLE away_superior_1_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="4" class="text-center">5 partidos</th>
							<th colspan="4" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center away-criteria-header">Away @A</th>
							<th class="text-center away-criteria-header">Away</th>
							<th class="text-center away-criteria-header">Avg Goals<br>Away</th>
							<th class="text-center home-criteria-header">Avg Conceded<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away @A</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Goals<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Conceded<br>Home</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_away_superior_1_5"><?php echo $prob_away_superior_1_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_away_superior_1_5"><?php echo $prob_away_superior_1_5_p1_away; ?>%</td>
							<td class="text-center value_num_away_superior_1_5"><?php echo number_format($avg_goals_p1_away, 2); ?></td>
							<td class="text-center value_num_away_conceded_superior_1_5"><?php echo number_format($avg_conceded_p1_home, 2); ?></td>
							<td class="text-center value_porc_away_superior_1_5 bg-gray-900"><?php echo $prob_away_superior_1_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_away_superior_1_5 bg-gray-900"><?php echo $prob_away_superior_1_5_p2_away; ?>%</td>
							<td class="text-center value_num_away_superior_1_5 bg-gray-900"><?php echo number_format($avg_goals_p2_away, 2); ?></td>
							<td class="text-center value_num_away_conceded_superior_1_5 bg-gray-900"><?php echo number_format($avg_conceded_p2_home, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table away_superior_1_5 ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_away_superior_1_5" name="porc_acierto_away_superior_1_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_away_superior_1_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_prob_away_superior_1_5" name="sub_agregar_prob_away_superior_1_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('away_superior_1_5', 51)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_away_superior_1_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB away_superior_1_5 ?>
				<?php #region region TAB ambos_marcan ?>
				<div class="tab-pane fade <?php echo ($tabselected == 5) ? "active show" : ""; ?>" id="default-tab-5">
					<?php #region region TABLE ambos_marcan ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="8" class="text-center">5 partidos</th>
							<th colspan="8" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center home-criteria-header">Home @H</th>
							<th class="text-center home-criteria-header">Home</th>
							<th class="text-center home-criteria-header">Avg Goals<br>Home</th>
							<th class="text-center home-criteria-header">Avg Conceded<br>Home</th>
							<th class="text-center away-criteria-header">Away @A</th>
							<th class="text-center away-criteria-header">Away</th>
							<th class="text-center away-criteria-header">Avg Goals<br>Away</th>
							<th class="text-center away-criteria-header">Avg Conceded<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home @H</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Goals<br>Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Conceded<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away @A</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Goals<br>Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Conceded<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_ambos_marcan"><?php echo $prob_ambos_marcan_p1_home_h; ?>%</td>
							<td class="text-center value_porc_ambos_marcan"><?php echo $prob_ambos_marcan_p1_home; ?>%</td>
							<td class="text-center value_num_ambos_marcan"><?php echo number_format($avg_goals_p1_home, 2); ?></td>
							<td class="text-center value_num_ambos_marcan"><?php echo number_format($avg_conceded_p1_home, 2); ?></td>
							<td class="text-center value_porc_ambos_marcan"><?php echo $prob_ambos_marcan_p1_away_a; ?>%</td>
							<td class="text-center value_porc_ambos_marcan"><?php echo $prob_ambos_marcan_p1_away; ?>%</td>
							<td class="text-center value_num_ambos_marcan"><?php echo number_format($avg_goals_p1_away, 2); ?></td>
							<td class="text-center value_num_ambos_marcan"><?php echo number_format($avg_conceded_p1_away, 2); ?></td>
							<td class="text-center value_porc_ambos_marcan bg-gray-900"><?php echo $prob_ambos_marcan_p2_home_h; ?>%</td>
							<td class="text-center value_porc_ambos_marcan bg-gray-900"><?php echo $prob_ambos_marcan_p2_home; ?>%</td>
							<td class="text-center value_num_ambos_marcan bg-gray-900"><?php echo number_format($avg_goals_p2_home, 2); ?></td>
							<td class="text-center value_num_ambos_marcan bg-gray-900"><?php echo number_format($avg_conceded_p2_home, 2); ?></td>
							<td class="text-center value_porc_ambos_marcan bg-gray-900"><?php echo $prob_ambos_marcan_p2_away_a; ?>%</td>
							<td class="text-center value_porc_ambos_marcan bg-gray-900"><?php echo $prob_ambos_marcan_p2_away; ?>%</td>
							<td class="text-center value_num_ambos_marcan bg-gray-900"><?php echo number_format($avg_goals_p2_away, 2); ?></td>
							<td class="text-center value_num_ambos_marcan bg-gray-900"><?php echo number_format($avg_conceded_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table ambos_marcan ?>
					
					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_ambos_marcan" name="porc_acierto_ambos_marcan" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_prob_ambos_marcan ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_prob_ambos_marcan" name="sub_agregar_prob_ambos_marcan" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('ambos_marcan', 52)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_prob_ambos_marcan ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB ambos_marcan ?>
			</div>
			<?php #endregion NAVTAB analisis ?>

			<h2>Corners Totales Superior:</h2>

			<?php #region region NAVTAB HEAD corners ?>
			<ul class="region_NAVTAB_HEAD nav nav-tabs fs-13px mt-3 no-border-radious">
				<li class="nav-item" onclick="tabselect_corners(7)">
					<a href="#corners-tab-7" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners == 7) ? "active" : ""; ?>">
						6.5
						<span id="porc_acierto_corners_6_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_6_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners(8)">
					<a href="#corners-tab-8" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners == 8) ? "active" : ""; ?>">
						7.5
						<span id="porc_acierto_corners_7_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_7_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners(9)">
					<a href="#corners-tab-9" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners == 9) ? "active" : ""; ?>">
						8.5
						<span id="porc_acierto_corners_8_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_8_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners(10)">
					<a href="#corners-tab-10" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners == 10) ? "active" : ""; ?>">
						9.5
						<span id="porc_acierto_corners_9_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_9_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners(11)">
					<a href="#corners-tab-11" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners == 11) ? "active" : ""; ?>">
						10.5
						<span id="porc_acierto_corners_10_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_10_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners(12)">
					<a href="#corners-tab-12" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners == 12) ? "active" : ""; ?>">
						11.5
						<span id="porc_acierto_corners_11_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_11_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners(13)">
					<a href="#corners-tab-13" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners == 13) ? "active" : ""; ?>">
						12.5
						<span id="porc_acierto_corners_12_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_12_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners(14)">
					<a href="#corners-tab-14" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners == 14) ? "active" : ""; ?>">
						13.5
						<span id="porc_acierto_corners_13_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_13_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
			</ul>
			<div class="tab-content panel rounded-0 rounded-bottom">
				<?php #region region TAB corners_6_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners == 7) ? "active show" : ""; ?>" id="corners-tab-7">
					<?php #region region TABLE corners_6_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center home-criteria-header">Home @H</th>
							<th class="text-center home-criteria-header">Home</th>
							<th class="text-center home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center away-criteria-header">Away @A</th>
							<th class="text-center away-criteria-header">Away</th>
							<th class="text-center away-criteria-header">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home @H</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away @A</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_6_5"><?php echo $prob_total_corners_masde_6_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_6_5"><?php echo $prob_total_corners_masde_6_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_6_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_6_5"><?php echo $prob_total_corners_masde_6_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_6_5"><?php echo $prob_total_corners_masde_6_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_6_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_6_5 bg-gray-900"><?php echo $prob_total_corners_masde_6_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_6_5 bg-gray-900"><?php echo $prob_total_corners_masde_6_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_6_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_6_5 bg-gray-900"><?php echo $prob_total_corners_masde_6_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_6_5 bg-gray-900"><?php echo $prob_total_corners_masde_6_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_6_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_6_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_6_5" name="porc_acierto_corners_6_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_6_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_6_5" name="sub_agregar_corners_6_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_6_5', 53)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_6_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_6_5 ?>
				<?php #region region TAB corners_7_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners == 8) ? "active show" : ""; ?>" id="corners-tab-8">
					<?php #region region TABLE corners_7_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center home-criteria-header">Home @H</th>
							<th class="text-center home-criteria-header">Home</th>
							<th class="text-center home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center away-criteria-header">Away @A</th>
							<th class="text-center away-criteria-header">Away</th>
							<th class="text-center away-criteria-header">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home @H</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away @A</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_7_5"><?php echo $prob_total_corners_masde_7_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_7_5"><?php echo $prob_total_corners_masde_7_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_7_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_7_5"><?php echo $prob_total_corners_masde_7_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_7_5"><?php echo $prob_total_corners_masde_7_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_7_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_7_5 bg-gray-900"><?php echo $prob_total_corners_masde_7_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_7_5 bg-gray-900"><?php echo $prob_total_corners_masde_7_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_7_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_7_5 bg-gray-900"><?php echo $prob_total_corners_masde_7_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_7_5 bg-gray-900"><?php echo $prob_total_corners_masde_7_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_7_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_7_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_7_5" name="porc_acierto_corners_7_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_7_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_7_5" name="sub_agregar_corners_7_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_7_5', 54)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_7_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_7_5 ?>
				<?php #region region TAB corners_8_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners == 9) ? "active show" : ""; ?>" id="corners-tab-9">
					<?php #region region TABLE corners_8_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center home-criteria-header">Home @H</th>
							<th class="text-center home-criteria-header">Home</th>
							<th class="text-center home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center away-criteria-header">Away @A</th>
							<th class="text-center away-criteria-header">Away</th>
							<th class="text-center away-criteria-header">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home @H</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away @A</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_8_5"><?php echo $prob_total_corners_masde_8_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_8_5"><?php echo $prob_total_corners_masde_8_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_8_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_8_5"><?php echo $prob_total_corners_masde_8_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_8_5"><?php echo $prob_total_corners_masde_8_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_8_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_8_5 bg-gray-900"><?php echo $prob_total_corners_masde_8_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_8_5 bg-gray-900"><?php echo $prob_total_corners_masde_8_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_8_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_8_5 bg-gray-900"><?php echo $prob_total_corners_masde_8_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_8_5 bg-gray-900"><?php echo $prob_total_corners_masde_8_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_8_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_8_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_8_5" name="porc_acierto_corners_8_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_8_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_8_5" name="sub_agregar_corners_8_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_8_5', 55)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_8_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_8_5 ?>
				<?php #region region TAB corners_9_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners == 10) ? "active show" : ""; ?>" id="corners-tab-10">
					<?php #region region TABLE corners_9_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center home-criteria-header">Home @H</th>
							<th class="text-center home-criteria-header">Home</th>
							<th class="text-center home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center away-criteria-header">Away @A</th>
							<th class="text-center away-criteria-header">Away</th>
							<th class="text-center away-criteria-header">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home @H</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away @A</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_9_5"><?php echo $prob_total_corners_masde_9_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_9_5"><?php echo $prob_total_corners_masde_9_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_9_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_9_5"><?php echo $prob_total_corners_masde_9_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_9_5"><?php echo $prob_total_corners_masde_9_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_9_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_9_5 bg-gray-900"><?php echo $prob_total_corners_masde_9_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_9_5 bg-gray-900"><?php echo $prob_total_corners_masde_9_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_9_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_9_5 bg-gray-900"><?php echo $prob_total_corners_masde_9_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_9_5 bg-gray-900"><?php echo $prob_total_corners_masde_9_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_9_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_9_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_9_5" name="porc_acierto_corners_9_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_9_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_9_5" name="sub_agregar_corners_9_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_9_5', 56)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_9_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_9_5 ?>
				<?php #region region TAB corners_10_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners == 11) ? "active show" : ""; ?>" id="corners-tab-11">
					<?php #region region TABLE corners_10_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_10_5"><?php echo $prob_total_corners_masde_10_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_10_5"><?php echo $prob_total_corners_masde_10_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_10_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_10_5"><?php echo $prob_total_corners_masde_10_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_10_5"><?php echo $prob_total_corners_masde_10_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_10_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_10_5 bg-gray-900"><?php echo $prob_total_corners_masde_10_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_10_5 bg-gray-900"><?php echo $prob_total_corners_masde_10_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_10_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_10_5 bg-gray-900"><?php echo $prob_total_corners_masde_10_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_10_5 bg-gray-900"><?php echo $prob_total_corners_masde_10_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_10_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_10_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_10_5" name="porc_acierto_corners_10_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_10_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_10_5" name="sub_agregar_corners_10_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_10_5', 57)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_10_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_10_5 ?>
				<?php #region region TAB corners_11_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners == 12) ? "active show" : ""; ?>" id="corners-tab-12">
					<?php #region region TABLE corners_11_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_11_5"><?php echo $prob_total_corners_masde_11_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_11_5"><?php echo $prob_total_corners_masde_11_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_11_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_11_5"><?php echo $prob_total_corners_masde_11_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_11_5"><?php echo $prob_total_corners_masde_11_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_11_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_11_5 bg-gray-900"><?php echo $prob_total_corners_masde_11_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_11_5 bg-gray-900"><?php echo $prob_total_corners_masde_11_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_11_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_11_5 bg-gray-900"><?php echo $prob_total_corners_masde_11_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_11_5 bg-gray-900"><?php echo $prob_total_corners_masde_11_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_11_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_11_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_11_5" name="porc_acierto_corners_11_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_11_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_11_5" name="sub_agregar_corners_11_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_11_5', 58)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_11_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_11_5 ?>
				<?php #region region TAB corners_12_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners == 13) ? "active show" : ""; ?>" id="corners-tab-13">
					<?php #region region TABLE corners_12_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_12_5"><?php echo $prob_total_corners_masde_12_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_12_5"><?php echo $prob_total_corners_masde_12_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_12_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_12_5"><?php echo $prob_total_corners_masde_12_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_12_5"><?php echo $prob_total_corners_masde_12_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_12_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_12_5 bg-gray-900"><?php echo $prob_total_corners_masde_12_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_12_5 bg-gray-900"><?php echo $prob_total_corners_masde_12_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_12_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_12_5 bg-gray-900"><?php echo $prob_total_corners_masde_12_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_12_5 bg-gray-900"><?php echo $prob_total_corners_masde_12_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_12_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_12_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_12_5" name="porc_acierto_corners_12_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_12_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_12_5" name="sub_agregar_corners_12_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_12_5', 59)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_12_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_12_5 ?>
				<?php #region region TAB corners_13_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners == 14) ? "active show" : ""; ?>" id="corners-tab-14">
					<?php #region region TABLE corners_13_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_13_5"><?php echo $prob_total_corners_masde_13_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_13_5"><?php echo $prob_total_corners_masde_13_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_13_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_13_5"><?php echo $prob_total_corners_masde_13_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_13_5"><?php echo $prob_total_corners_masde_13_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_13_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_13_5 bg-gray-900"><?php echo $prob_total_corners_masde_13_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_13_5 bg-gray-900"><?php echo $prob_total_corners_masde_13_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_13_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_13_5 bg-gray-900"><?php echo $prob_total_corners_masde_13_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_13_5 bg-gray-900"><?php echo $prob_total_corners_masde_13_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_13_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_13_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_13_5" name="porc_acierto_corners_13_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_13_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_13_5" name="sub_agregar_corners_13_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_13_5', 60)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_13_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_13_5 ?>
			</div>
			<?php #endregion NAVTAB corners ?>

			<h2>Corners Totales Inferior:</h2>

			<?php #region region NAVTAB HEAD corners_inferior ?>
			<ul class="region_NAVTAB_HEAD nav nav-tabs fs-13px mt-3 no-border-radious">
				<li class="nav-item" onclick="tabselect_corners_inferior(7)">
					<a href="#corners-inferior-tab-7" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners_inferior == 7) ? "active" : ""; ?>">
						6.5
						<span id="porc_acierto_corners_inferior_6_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_inferior_6_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners_inferior(8)">
					<a href="#corners-inferior-tab-8" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners_inferior == 8) ? "active" : ""; ?>">
						7.5
						<span id="porc_acierto_corners_inferior_7_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_inferior_7_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners_inferior(9)">
					<a href="#corners-inferior-tab-9" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners_inferior == 9) ? "active" : ""; ?>">
						8.5
						<span id="porc_acierto_corners_inferior_8_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_inferior_8_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners_inferior(10)">
					<a href="#corners-inferior-tab-10" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners_inferior == 10) ? "active" : ""; ?>">
						9.5
						<span id="porc_acierto_corners_inferior_9_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_inferior_9_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners_inferior(11)">
					<a href="#corners-inferior-tab-11" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners_inferior == 11) ? "active" : ""; ?>">
						10.5
						<span id="porc_acierto_corners_inferior_10_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_inferior_10_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners_inferior(12)">
					<a href="#corners-inferior-tab-12" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners_inferior == 12) ? "active" : ""; ?>">
						11.5
						<span id="porc_acierto_corners_inferior_11_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_inferior_11_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners_inferior(13)">
					<a href="#corners-inferior-tab-13" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners_inferior == 13) ? "active" : ""; ?>">
						12.5
						<span id="porc_acierto_corners_inferior_12_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_inferior_12_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect_corners_inferior(14)">
					<a href="#corners-inferior-tab-14" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected_corners_inferior == 14) ? "active" : ""; ?>">
						13.5
						<span id="porc_acierto_corners_inferior_13_5_badge" class="badge bg-primary rounded-25 fs-12px ms-1">
					</span>
					<?php if (!empty($existing_bets['corner_inferior_13_5'])): ?>
						<i class="fas fa-check-circle text-info ms-1" title="Bet already exists for this match and bet type"></i>
					<?php endif; ?>
					</a>
				</li>
			</ul>
			<div class="tab-content panel rounded-0 rounded-bottom">
				<?php #region region TAB corners_inferior_6_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners_inferior == 7) ? "active show" : ""; ?>" id="corners-inferior-tab-7">
					<?php #region region TABLE corners_inferior_6_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center home-criteria-header">Home @H</th>
							<th class="text-center home-criteria-header">Home</th>
							<th class="text-center home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center away-criteria-header">Away @A</th>
							<th class="text-center away-criteria-header">Away</th>
							<th class="text-center away-criteria-header">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home @H</th>
							<th class="text-center bg-gray-900 home-criteria-header">Home</th>
							<th class="text-center bg-gray-900 home-criteria-header">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away @A</th>
							<th class="text-center bg-gray-900 away-criteria-header">Away</th>
							<th class="text-center bg-gray-900 away-criteria-header">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_inferior_6_5"><?php echo $prob_total_corners_menosde_6_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_6_5"><?php echo $prob_total_corners_menosde_6_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_6_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_6_5"><?php echo $prob_total_corners_menosde_6_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_6_5"><?php echo $prob_total_corners_menosde_6_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_6_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_6_5 bg-gray-900"><?php echo $prob_total_corners_menosde_6_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_6_5 bg-gray-900"><?php echo $prob_total_corners_menosde_6_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_6_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_6_5 bg-gray-900"><?php echo $prob_total_corners_menosde_6_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_6_5 bg-gray-900"><?php echo $prob_total_corners_menosde_6_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_6_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_inferior_6_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_inferior_6_5" name="porc_acierto_corners_inferior_6_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_inferior_6_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_inferior_6_5" name="sub_agregar_corners_inferior_6_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_inferior_6_5', 61)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_inferior_6_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_inferior_6_5 ?>
				<?php #region region TAB corners_inferior_7_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners_inferior == 8) ? "active show" : ""; ?>" id="corners-inferior-tab-8">
					<?php #region region TABLE corners_inferior_7_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_inferior_7_5"><?php echo $prob_total_corners_menosde_7_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_7_5"><?php echo $prob_total_corners_menosde_7_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_7_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_7_5"><?php echo $prob_total_corners_menosde_7_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_7_5"><?php echo $prob_total_corners_menosde_7_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_7_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_7_5 bg-gray-900"><?php echo $prob_total_corners_menosde_7_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_7_5 bg-gray-900"><?php echo $prob_total_corners_menosde_7_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_7_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_7_5 bg-gray-900"><?php echo $prob_total_corners_menosde_7_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_7_5 bg-gray-900"><?php echo $prob_total_corners_menosde_7_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_7_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_inferior_7_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_inferior_7_5" name="porc_acierto_corners_inferior_7_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_inferior_7_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_inferior_7_5" name="sub_agregar_corners_inferior_7_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_inferior_7_5', 62)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_inferior_7_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_inferior_7_5 ?>
				<?php #region region TAB corners_inferior_8_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners_inferior == 9) ? "active show" : ""; ?>" id="corners-inferior-tab-9">
					<?php #region region TABLE corners_inferior_8_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_inferior_8_5"><?php echo $prob_total_corners_menosde_8_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_8_5"><?php echo $prob_total_corners_menosde_8_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_8_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_8_5"><?php echo $prob_total_corners_menosde_8_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_8_5"><?php echo $prob_total_corners_menosde_8_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_8_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_8_5 bg-gray-900"><?php echo $prob_total_corners_menosde_8_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_8_5 bg-gray-900"><?php echo $prob_total_corners_menosde_8_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_8_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_8_5 bg-gray-900"><?php echo $prob_total_corners_menosde_8_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_8_5 bg-gray-900"><?php echo $prob_total_corners_menosde_8_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_8_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_inferior_8_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_inferior_8_5" name="porc_acierto_corners_inferior_8_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_inferior_8_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_inferior_8_5" name="sub_agregar_corners_inferior_8_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_inferior_8_5', 63)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_inferior_8_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_inferior_8_5 ?>
				<?php #region region TAB corners_inferior_9_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners_inferior == 10) ? "active show" : ""; ?>" id="corners-inferior-tab-10">
					<?php #region region TABLE corners_inferior_9_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_inferior_9_5"><?php echo $prob_total_corners_menosde_9_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_9_5"><?php echo $prob_total_corners_menosde_9_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_9_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_9_5"><?php echo $prob_total_corners_menosde_9_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_9_5"><?php echo $prob_total_corners_menosde_9_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_9_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_9_5 bg-gray-900"><?php echo $prob_total_corners_menosde_9_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_9_5 bg-gray-900"><?php echo $prob_total_corners_menosde_9_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_9_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_9_5 bg-gray-900"><?php echo $prob_total_corners_menosde_9_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_9_5 bg-gray-900"><?php echo $prob_total_corners_menosde_9_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_9_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_inferior_9_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_inferior_9_5" name="porc_acierto_corners_inferior_9_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_inferior_9_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_inferior_9_5" name="sub_agregar_corners_inferior_9_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_inferior_9_5', 64)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_inferior_9_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_inferior_9_5 ?>
				<?php #region region TAB corners_inferior_10_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners_inferior == 11) ? "active show" : ""; ?>" id="corners-inferior-tab-11">
					<?php #region region TABLE corners_inferior_10_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_inferior_10_5"><?php echo $prob_total_corners_menosde_10_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_10_5"><?php echo $prob_total_corners_menosde_10_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_10_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_10_5"><?php echo $prob_total_corners_menosde_10_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_10_5"><?php echo $prob_total_corners_menosde_10_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_10_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_10_5 bg-gray-900"><?php echo $prob_total_corners_menosde_10_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_10_5 bg-gray-900"><?php echo $prob_total_corners_menosde_10_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_10_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_10_5 bg-gray-900"><?php echo $prob_total_corners_menosde_10_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_10_5 bg-gray-900"><?php echo $prob_total_corners_menosde_10_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_10_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_inferior_10_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_inferior_10_5" name="porc_acierto_corners_inferior_10_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_inferior_10_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_inferior_10_5" name="sub_agregar_corners_inferior_10_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_inferior_10_5', 65)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_inferior_10_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_inferior_10_5 ?>
				<?php #region region TAB corners_inferior_11_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners_inferior == 12) ? "active show" : ""; ?>" id="corners-inferior-tab-12">
					<?php #region region TABLE corners_inferior_11_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_inferior_11_5"><?php echo $prob_total_corners_menosde_11_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_11_5"><?php echo $prob_total_corners_menosde_11_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_11_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_11_5"><?php echo $prob_total_corners_menosde_11_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_11_5"><?php echo $prob_total_corners_menosde_11_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_11_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_11_5 bg-gray-900"><?php echo $prob_total_corners_menosde_11_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_11_5 bg-gray-900"><?php echo $prob_total_corners_menosde_11_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_11_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_11_5 bg-gray-900"><?php echo $prob_total_corners_menosde_11_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_11_5 bg-gray-900"><?php echo $prob_total_corners_menosde_11_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_11_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_inferior_11_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_inferior_11_5" name="porc_acierto_corners_inferior_11_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_inferior_11_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_inferior_11_5" name="sub_agregar_corners_inferior_11_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_inferior_11_5', 66)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_inferior_11_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_inferior_11_5 ?>
				<?php #region region TAB corners_inferior_12_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners_inferior == 13) ? "active show" : ""; ?>" id="corners-inferior-tab-13">
					<?php #region region TABLE corners_inferior_12_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_inferior_12_5"><?php echo $prob_total_corners_menosde_12_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_12_5"><?php echo $prob_total_corners_menosde_12_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_12_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_12_5"><?php echo $prob_total_corners_menosde_12_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_12_5"><?php echo $prob_total_corners_menosde_12_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_12_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_12_5 bg-gray-900"><?php echo $prob_total_corners_menosde_12_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_12_5 bg-gray-900"><?php echo $prob_total_corners_menosde_12_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_12_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_12_5 bg-gray-900"><?php echo $prob_total_corners_menosde_12_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_12_5 bg-gray-900"><?php echo $prob_total_corners_menosde_12_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_12_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_inferior_12_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_inferior_12_5" name="porc_acierto_corners_inferior_12_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_inferior_12_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_inferior_12_5" name="sub_agregar_corners_inferior_12_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_inferior_12_5', 67)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_inferior_12_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_inferior_12_5 ?>
				<?php #region region TAB corners_inferior_13_5 ?>
				<div class="tab-pane fade <?php echo ($tabselected_corners_inferior == 14) ? "active show" : ""; ?>" id="corners-inferior-tab-14">
					<?php #region region TABLE corners_inferior_13_5 ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th colspan="6" class="text-center">5 partidos</th>
							<th colspan="6" class="text-center bg-gray-900">10 partidos</th>
						</tr>
						<tr>
							<th class="text-center">Home @H</th>
							<th class="text-center">Home</th>
							<th class="text-center">Avg Corners<br>Home</th>
							<th class="text-center">Away @A</th>
							<th class="text-center">Away</th>
							<th class="text-center">Avg Corners<br>Away</th>
							<th class="text-center bg-gray-900">Home @H</th>
							<th class="text-center bg-gray-900">Home</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Home</th>
							<th class="text-center bg-gray-900">Away @A</th>
							<th class="text-center bg-gray-900">Away</th>
							<th class="text-center bg-gray-900">Avg Corners<br>Away</th>
						</tr>
						</thead>
						<tbody class="fs-11px">
						<tr class="cursor-pointer">
							<td class="text-center value_porc_corners_inferior_13_5"><?php echo $prob_total_corners_menosde_13_5_p1_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_13_5"><?php echo $prob_total_corners_menosde_13_5_p1_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_13_5"><?php echo number_format($avg_total_corners_p1_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_13_5"><?php echo $prob_total_corners_menosde_13_5_p1_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_13_5"><?php echo $prob_total_corners_menosde_13_5_p1_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_13_5"><?php echo number_format($avg_total_corners_p1_away, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_13_5 bg-gray-900"><?php echo $prob_total_corners_menosde_13_5_p2_home_h; ?>%</td>
							<td class="text-center value_porc_corners_inferior_13_5 bg-gray-900"><?php echo $prob_total_corners_menosde_13_5_p2_home; ?>%</td>
							<td class="text-center value_num_corners_inferior_13_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_home, 2); ?></td>
							<td class="text-center value_porc_corners_inferior_13_5 bg-gray-900"><?php echo $prob_total_corners_menosde_13_5_p2_away_a; ?>%</td>
							<td class="text-center value_porc_corners_inferior_13_5 bg-gray-900"><?php echo $prob_total_corners_menosde_13_5_p2_away; ?>%</td>
							<td class="text-center value_num_corners_inferior_13_5 bg-gray-900"><?php echo number_format($avg_total_corners_p2_away, 2); ?></td>
						</tr>
						</tbody>
					</table>
					<?php #endregion table corners_inferior_13_5 ?>

					<!-- BEGIN text -->
					<div class="col-md-4 col-xs-12 ms-2 pb-2">
						<div class="input-group">
                            <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                % acierto:
                            </span>
							<input type="text" id="porc_acierto_corners_inferior_13_5" name="porc_acierto_corners_inferior_13_5" class="form-control form-control-fh fs-12px no-border-radious"/>
						</div>
					</div>
					<!-- END text -->
					<!-- BEGIN row -->
					<div class="row mt-3">
						<?php #region region SUBMIT sub_agregar_corners_inferior_13_5 ?>
						<div class="col-md-12 col-xs-12">
							<button type="button" id="sub_agregar_corners_inferior_13_5" name="sub_agregar_corners_inferior_13_5" class="btn btn-xs btn-success w-100 no-border-radious" onclick="showBettingModal('corner_inferior_13_5', 68)">
								Agregar
							</button>
						</div>
						<?php #endregion SUBMIT sub_agregar_corners_inferior_13_5 ?>
					</div>
					<!-- END row -->
				</div>
				<?php #endregion TAB corners_inferior_13_5 ?>
			</div>
			<?php #endregion NAVTAB corners_inferior ?>

			<!-- BEGIN row -->
			<div class="row mt-3">
				<?php #region region SUBMIT sub_marcar_revisado_probabilidades ?>
				<div class="col-md-12 col-xs-12">
					<button type="submit" id="sub_marcar_revisado_probabilidades" name="sub_marcar_revisado_probabilidades" class="btn btn-sm btn-success w-100 no-border-radious">
						Marcar como revisado
					</button>
				</div>
				<?php #endregion SUBMIT sub_marcar_revisado_probabilidades ?>
			</div>
			<!-- END row -->
			<?php #region region PANEL partidos_infos ?>
			<div class="panel panel-inverse mt-3 no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">
						Historico
						<span class="badge bg-primary rounded-0 fs-10px" id="historico-counter">
                            <?php echo count($partidos_infos); ?>
                        </span>
					</h4>
				</div>
				<!-- BEGIN filter buttons -->
				<div class="panel-body p-2">
					<div class="row mb-2">
						<div class="col-12">
							<div class="btn-group btn-group-toggle w-100" data-toggle="buttons" id="historico-filters">
								<label class="btn btn-secondary active" data-filter="todos">
									<input type="radio" name="historico_filter" autocomplete="off" value="todos" checked> Todos
								</label>
								<label class="btn btn-secondary" data-filter="home">
									<input type="radio" name="historico_filter" autocomplete="off" value="home"> Home
								</label>
								<label class="btn btn-secondary" data-filter="home_h">
									<input type="radio" name="historico_filter" autocomplete="off" value="home_h"> Home @H
								</label>
								<label class="btn btn-secondary" data-filter="home_a">
									<input type="radio" name="historico_filter" autocomplete="off" value="home_a"> Home @A
								</label>
								<label class="btn btn-secondary" data-filter="away">
									<input type="radio" name="historico_filter" autocomplete="off" value="away"> Away
								</label>
								<label class="btn btn-secondary" data-filter="away_a">
									<input type="radio" name="historico_filter" autocomplete="off" value="away_a"> Away @A
								</label>
								<label class="btn btn-secondary" data-filter="away_h">
									<input type="radio" name="historico_filter" autocomplete="off" value="away_h"> Away @H
								</label>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-12">
							<button type="button" class="btn btn-xs btn-success" id="export-excel-btn">
								<i class="fa fa-file-excel"></i> Export Excel
							</button>
						</div>
					</div>
				</div>
				<!-- END filter buttons -->
				<!-- BEGIN PANEL body -->
				<div class="p-1 table-nowrap" style="overflow: auto; height: 300px">
					<?php #region region TABLE partidos_infos ?>
					<table id="historico-table" class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="text-center">#</th>
							<th class="text-center">Fecha</th>
							<th class="text-center">Days Ago</th>
							<th class="text-center">Home</th>
							<th class="text-center">Home corners</th>
							<th class="text-center">Marcador</th>
							<th class="text-center">Away corners</th>
							<th class="text-center">Away</th>
							<th class="text-center">Total corners</th>
							<th class="text-start">Torneo</th>
							<th class="text-center">Temporada</th>
						</tr>
						</thead>
						<tbody class="fs-10px">
						<?php foreach ($partidos_infos as $partido_info): ?>
							<?php
							$current_row = $n_row_partidos_infos;
							$separator_class = '';
							if ($current_row == 5) {
								$separator_class = ' historical-separator-5';
							} elseif ($current_row == 10) {
								$separator_class = ' historical-separator-10';
							}

							// Calculate days ago using Bogotá timezone
							setTimeZoneCol(); // Set Bogotá timezone
							$fecha_actual = create_date();
							$days_ago = '';
							if (!empty($partido_info->fecha)) {
								$days_diff = getDateDiffDaysNonLiteral($partido_info->fecha, $fecha_actual);
								$days_ago = abs($days_diff); // Always show positive number for "days ago"
							}
							?>
							<tr class="cursor-pointer<?php echo $separator_class; ?>">
								<td class="text-center"><?php echo $n_row_partidos_infos++; ?></td>
								<td class="text-center"><?php echo $partido_info->fecha; ?></td>
								<td class="text-center"><?php echo $days_ago; ?></td>
								<td class="text-center fw-bold <?php echo ($partido_info->home == $cur_partido->home) ? 'text-primary' : (($partido_info->home == $cur_partido->away) ? 'text-warning' : ''); ?>"><?php echo $partido_info->home; ?></td>
								<td class="text-center"><?php echo $partido_info->homecorners; ?></td>
								<td class="text-center">
									<span class="badge bg-secondary fs-11px fw-bold px-2 py-1">
										<?php echo $partido_info->homegoals . ' - ' . $partido_info->awaygoals; ?>
									</span>
								</td>
								<td class="text-center"><?php echo $partido_info->awaycorners; ?></td>
								<td class="text-center fw-bold <?php echo ($partido_info->away == $cur_partido->home) ? 'text-primary' : (($partido_info->away == $cur_partido->away) ? 'text-warning' : ''); ?>"><?php echo $partido_info->away; ?></td>
								<td class="text-center"><?php echo ($partido_info->homecorners + $partido_info->awaycorners); ?></td>
								<td class="text-start"><?php echo $partido_info->nom_pais; ?></td>
								<td class="text-center"><?php echo $partido_info->season; ?></td>
							</tr>
						<?php endforeach; ?>
						</tbody>
					</table>
					<?php #endregion TABLE partidos_infos ?>
				</div>
				<!-- END PANEL body -->
			</div>
			<?php #endregion PANEL partidos_infos ?>
		</form>
		<?php #endregion FORM ?>

		<!-- BEGIN Upload Torneo Modal -->
		<div class="modal fade" id="uploadTorneoModal" tabindex="-1" aria-labelledby="uploadTorneoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="uploadTorneoModalLabel">
							<i class="fas fa-upload me-2"></i>Cargar datos del torneo
						</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<form action="epartido_probabilidades" method="POST" enctype="multipart/form-data">
						<input type="hidden" id="id_partido_modal" name="id_partido" value="<?php echo @recover_var($id_partido) ?>">
						<input type="hidden" id="tabselected_modal" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">
						<input type="hidden" id="tabselected_corners_modal" name="tabselected_corners" value="<?php echo limpiar_datos($tabselected_corners); ?>">
						<input type="hidden" id="pais_upload_id" name="pais_upload_id" value="">

						<div class="modal-body">
							<!-- File Upload -->
							<div class="row mb-3">
								<div class="col-12">
									<label for="archivocsv_modal" class="form-label">Archivo CSV:</label>
									<input type="file" class="form-control" id="archivocsv_modal" name="archivocsv" accept=".csv" required>
									<div class="form-text text-muted">Seleccione un archivo CSV con los datos del torneo.</div>
								</div>
							</div>
							
							<!-- Tournament Display -->
							<div class="row mb-3">
								<div class="col-12">
									<label class="form-label">Torneo:</label>
									<div class="form-control bg-secondary text-white" id="torneo_display" readonly></div>
								</div>
							</div>

							<!-- Season Input -->
							<div class="row mb-3">
								<div class="col-12">
									<label for="season_upload" class="form-label">Temporada:</label>
									<input type="text" class="form-control" id="season_upload" name="season_upload" required>
								</div>
							</div>
						</div>

						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" name="sub_upload_torneo" class="btn btn-success">
								<i class="fas fa-upload me-1"></i>Subir archivo
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<!-- END Upload Torneo Modal -->

		<!-- BEGIN Create Tournament Modal -->
		<div class="modal fade" id="createTournamentModal" tabindex="-1" aria-labelledby="createTournamentModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="createTournamentModalLabel">
							<i class="fas fa-plus me-2"></i>Create New Tournament
						</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<form id="createTournamentForm">
						<div class="modal-body">
							<div class="row mb-3">
								<div class="col-12">
									<label for="tournament_name_create" class="form-label">Tournament Name:</label>
									<input type="text" class="form-control" id="tournament_name_create" name="tournament_name" required>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
							<button type="submit" class="btn btn-success">
								<i class="fas fa-save me-1"></i>Create Tournament
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<!-- END Create Tournament Modal -->

		<!-- BEGIN Edit Tournament Modal -->
		<div class="modal fade" id="editTournamentModal" tabindex="-1" aria-labelledby="editTournamentModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="editTournamentModalLabel">
							<i class="fas fa-edit me-2"></i>Edit Tournament
						</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<form id="editTournamentForm">
						<input type="hidden" id="tournament_id_edit" name="tournament_id">
						<div class="modal-body">
							<div class="row mb-3">
								<div class="col-12">
									<label for="tournament_name_edit" class="form-label">Tournament Name:</label>
									<input type="text" class="form-control" id="tournament_name_edit" name="tournament_name" required>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
							<button type="submit" class="btn btn-success">
								<i class="fas fa-save me-1"></i>Update Tournament
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<!-- END Edit Tournament Modal -->

		<!-- BEGIN Add Tournament Modal -->
		<div class="modal fade" id="addTournamentModal" tabindex="-1" aria-labelledby="addTournamentModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="addTournamentModalLabel">
							<i class="fas fa-plus-circle me-2"></i>Add Tournament
						</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="row mb-3">
							<div class="col-12">
								<label for="id_pais_modal" class="form-label">Select Tournament:</label>
    				<select id="id_pais_modal" class="form-select">
									<option value="">-- Select Tournament --</option>
									<?php foreach ($paises as $pais): ?>
										<option value="<?php echo limpiar_datos($pais->id); ?>">
											<?php echo limpiar_datos($pais->nombre); ?>
										</option>
									<?php endforeach; ?>
								</select>
							</div>
						</div>
						<div class="row mb-3">
							<div class="col-12">
								<label for="season_modal" class="form-label">Season (Optional):</label>
								<input type="text" class="form-control" id="season_modal" name="season" placeholder="e.g., 2023-24">
								<div class="form-text text-muted">Leave empty to add tournament without specific season</div>
								<!-- Validation indicator -->
								<div id="season_validation_indicator" class="mt-2" style="display: none;">
									<div id="season_validation_content"></div>
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
							<i class="fas fa-times me-1"></i>Cancel
						</button>
						<button type="button" class="btn btn-success" onclick="addSelectedTournamentFromModal()">
							<i class="fas fa-check me-1"></i>Solo agregar torneo
						</button>
						<button type="button" class="btn btn-primary" id="addTournamentAndUpdateBtn" onclick="addTournamentAndUpdateViaAPI()" style="display: none;">
							<i class="fas fa-plus-circle me-1"></i>Agregar torneo y actualizar via API
						</button>
					</div>
				</div>
			</div>
		</div>
		<!-- END Add Tournament Modal -->

		<!-- BEGIN Betting Modal -->
		<div class="modal fade" id="bettingModal" tabindex="-1" aria-labelledby="bettingModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="bettingModalLabel">
							<i class="fas fa-dice me-2"></i>Agregar Apuesta - <span id="betting_tab_name">Home Superior 0.5</span>
						</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="row mb-3">
							<div class="col-12">
								<label for="betting_cuota" class="form-label">Cuota:</label>
								<input type="number"
									   class="form-control"
									   id="betting_cuota"
									   name="betting_cuota"
									   step="0.01"
									   min="1.01"
									   placeholder="Ej: 2.50"
									   required>
								<div class="form-text text-muted">Ingrese la cuota para esta apuesta (debe ser mayor a 1.00)</div>
							</div>
						</div>

						<!-- Criteria Preview -->
						<div class="row mb-3">
							<div class="col-12">
								<label class="form-label">Criterios que se guardarán:</label>
								<div class="card bg-secondary">
									<div class="card-body">
										<div id="criteria_preview" class="fs-12px">
											<!-- Criteria will be populated by JavaScript -->
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
							<i class="fas fa-times me-1"></i>Cancelar
						</button>
						<button type="button" class="btn btn-success" id="btn_confirm_bet">
							<i class="fas fa-check me-1"></i>Confirmar Apuesta
						</button>
					</div>
				</div>
			</div>
		</div>
		<!-- END Betting Modal -->
	</div>
	<!-- END #content -->
	
	<!-- BEGIN floating team badges -->
	<div class="floating-team-badges">
		<div class="btn btn-primary floating-badge home-badge" id="floating-home-badge" onclick="copyTeamWithToast('floating-home')" title="Click to copy home team">
			<i class="fas fa-home"></i>
			<span id="floating-home"><?php echo @recover_var($cur_partido->home) ?></span>
		</div>
		<div class="btn btn-warning floating-badge away-badge" id="floating-away-badge" onclick="copyTeamWithToast('floating-away')" title="Click to copy away team">
			<i class="fas fa-plane"></i>
			<span id="floating-away"><?php echo @recover_var($cur_partido->away) ?></span>
		</div>
	</div>
	<!-- END floating team badges -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
<?php #region region js select2 ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/select2/dist/js/select2.min.js"></script>

<style>
/* Dark theme styles for Select2 in modals */
.modal .select2-container--default .select2-selection--single {
    background-color: #2d353c !important;
    border: 1px solid #495057 !important;
    color: white !important;
    height: calc(2.25rem + 2px) !important;
}

.modal .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: white !important;
    line-height: calc(2.25rem) !important;
    padding-left: 12px !important;
}

.modal .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #6c757d !important;
}

.modal .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: calc(2.25rem) !important;
}

.modal .select2-dropdown {
    background-color: #2d353c !important;
    border: 1px solid #495057 !important;
    color: white !important;
}

.modal .select2-results__option {
    background-color: #2d353c !important;
    color: white !important;
}

.modal .select2-results__option--highlighted[aria-selected] {
    background-color: #0d6efd !important;
    color: white !important;
}

.modal .select2-search--dropdown .select2-search__field {
    background-color: #2d353c !important;
    border: 1px solid #495057 !important;
    color: white !important;
}
</style>

<script type="text/javascript">
    $(".default-select2").select2();

    // Focus the search input when the dropdown is opened
    $('#id_pais').on('select2:open', function () {
        document.querySelector('.select2-search__field').focus();
    });
</script>
<?php #endregion js select2 ?>
<?php #region region JS tabselect ?>
<script type="text/javascript">
    function tabselect(ntab) {
        document.getElementById('tabselected').value = ntab;
    }

    function tabselect_corners(ntab) {
        document.getElementById('tabselected_corners').value = ntab;
    }

    function tabselect_corners_inferior(ntab) {
        document.getElementById('tabselected_corners_inferior').value = ntab;
    }
</script>
<?php #endregion JS tabselect ?>

<?php #region region JS colorear un TD si el contenido cumple una condicion y calcular % acierto ?>
<script>
    //resaltar las celdas que tengan -1 para indicar que faltan partidos para los calculos de probabilidades
    document.querySelectorAll('.text-center').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value === -1) {
            cell.classList.add('text-warning');
        }
    });
    
    let total_count_1_5               = 0;
    let total_count_home_marca        = 0;
    let total_count_home_superior_1_5 = 0;
    let total_count_away_marca        = 0;
    let total_count_away_superior_1_5 = 0;
    let total_count_ambos_marcan      = 0;
    let total_count_corners_6_5       = 0;
    let total_count_corners_7_5       = 0;
    let total_count_corners_8_5       = 0;
    let total_count_corners_9_5       = 0;
    let total_count_corners_10_5      = 0;
    let total_count_corners_11_5      = 0;
    let total_count_corners_12_5      = 0;
    let total_count_corners_13_5      = 0;
    let total_criterios               = 16;
    let total_criterios_team_marca    = 8;
    let total_criterios_corners_6_5   = 12;
    let total_criterios_corners_7_5   = 12;
    let total_criterios_corners_8_5   = 12;
    let total_criterios_corners_9_5   = 12;
    let total_criterios_corners_10_5  = 12;
    let total_criterios_corners_11_5  = 12;
    let total_criterios_corners_12_5  = 12;
    let total_criterios_corners_13_5  = 12;
    
    document.querySelectorAll('.value_porc_1_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_1_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 1.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_1_5++; // Increment the total counter
        }
    });
    
    document.querySelectorAll('.value_porc_home_marca').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_home_marca++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_home_marca').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 1.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_home_marca++; // Increment the total counter
        }
    });
    
    document.querySelectorAll('.value_porc_home_superior_1_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_home_superior_1_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_home_superior_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 2.50) {
            cell.classList.add('text-success');
            total_count_home_superior_1_5++;
        }
    });
    document.querySelectorAll('.value_num_home_conceded_superior_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 2.00) {
            cell.classList.add('text-success');
            total_count_home_superior_1_5++;
        }
    });
    
    document.querySelectorAll('.value_porc_away_marca').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_marca++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_away_marca').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 1.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_marca++; // Increment the total counter
        }
    });
    
    document.querySelectorAll('.value_porc_away_superior_1_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_superior_1_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_away_superior_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 2.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_superior_1_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_away_conceded_superior_1_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number
        
        if (value >= 2.00) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_away_superior_1_5++; // Increment the total counter
        }
    });
    
    document.querySelectorAll('.value_porc_ambos_marcan').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number
        
        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_ambos_marcan++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_ambos_marcan').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value >= 1.50) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_ambos_marcan++; // Increment the total counter
        }
    });

    // Corners 6.5 calculations
    document.querySelectorAll('.value_porc_corners_6_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_corners_6_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_6_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value >= 7.0) { // 6.5 + 0.5 buffer
            cell.classList.add('text-success'); // Add the class if the value is greater than 7.0
            total_count_corners_6_5++; // Increment the total counter
        }
    });

    // Corners 7.5 calculations
    document.querySelectorAll('.value_porc_corners_7_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_corners_7_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_7_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value >= 8.0) { // 7.5 + 0.5 buffer
            cell.classList.add('text-success'); // Add the class if the value is greater than 8.0
            total_count_corners_7_5++; // Increment the total counter
        }
    });

    // Corners 8.5 calculations
    document.querySelectorAll('.value_porc_corners_8_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_corners_8_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_8_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value >= 9.0) { // 8.5 + 0.5 buffer
            cell.classList.add('text-success'); // Add the class if the value is greater than 9.0
            total_count_corners_8_5++; // Increment the total counter
        }
    });

    // Corners 9.5 calculations
    document.querySelectorAll('.value_porc_corners_9_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_corners_9_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_9_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value >= 10.0) { // 9.5 + 0.5 buffer
            cell.classList.add('text-success'); // Add the class if the value is greater than 10.0
            total_count_corners_9_5++; // Increment the total counter
        }
    });

    // Corners 10.5 calculations
    document.querySelectorAll('.value_porc_corners_10_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_corners_10_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_10_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value >= 11.0) { // 10.5 + 0.5 buffer
            cell.classList.add('text-success'); // Add the class if the value is greater than 11.0
            total_count_corners_10_5++; // Increment the total counter
        }
    });

    // Corners 11.5 calculations
    document.querySelectorAll('.value_porc_corners_11_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_corners_11_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_11_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value >= 12.0) { // 11.5 + 0.5 buffer
            cell.classList.add('text-success'); // Add the class if the value is greater than 12.0
            total_count_corners_11_5++; // Increment the total counter
        }
    });

    // Corners 12.5 calculations
    document.querySelectorAll('.value_porc_corners_12_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_corners_12_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_12_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value >= 13.0) { // 12.5 + 0.5 buffer
            cell.classList.add('text-success'); // Add the class if the value is greater than 13.0
            total_count_corners_12_5++; // Increment the total counter
        }
    });

    // Corners 13.5 calculations
    document.querySelectorAll('.value_porc_corners_13_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) {
            cell.classList.add('text-success'); // Add the class if the value is greater than 70
            total_count_corners_13_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_13_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value >= 14.0) { // 13.5 + 0.5 buffer
            cell.classList.add('text-success'); // Add the class if the value is greater than 14.0
            total_count_corners_13_5++; // Increment the total counter
        }
    });

    let porcentaje_acierto_1_5               = Math.round((total_count_1_5 * 100) / total_criterios);
    let porcentaje_acierto_home_marca        = Math.round((total_count_home_marca * 100) / total_criterios_team_marca);
    let porcentaje_acierto_home_superior_1_5 = Math.round((total_count_home_superior_1_5 * 100) / total_criterios_team_marca);
    let porcentaje_acierto_away_marca        = Math.round((total_count_away_marca * 100) / total_criterios_team_marca);
    let porcentaje_acierto_away_superior_1_5 = Math.round((total_count_away_superior_1_5 * 100) / total_criterios_team_marca);
    let porcentaje_acierto_ambos_marcan      = Math.round((total_count_ambos_marcan * 100) / total_criterios);
    let porcentaje_acierto_corners_6_5       = Math.round((total_count_corners_6_5 * 100) / total_criterios_corners_6_5);
    let porcentaje_acierto_corners_7_5       = Math.round((total_count_corners_7_5 * 100) / total_criterios_corners_7_5);
    let porcentaje_acierto_corners_8_5       = Math.round((total_count_corners_8_5 * 100) / total_criterios_corners_8_5);
    let porcentaje_acierto_corners_9_5       = Math.round((total_count_corners_9_5 * 100) / total_criterios_corners_9_5);
    let porcentaje_acierto_corners_10_5      = Math.round((total_count_corners_10_5 * 100) / total_criterios_corners_10_5);
    let porcentaje_acierto_corners_11_5      = Math.round((total_count_corners_11_5 * 100) / total_criterios_corners_11_5);
    let porcentaje_acierto_corners_12_5      = Math.round((total_count_corners_12_5 * 100) / total_criterios_corners_12_5);
    let porcentaje_acierto_corners_13_5      = Math.round((total_count_corners_13_5 * 100) / total_criterios_corners_13_5);
    
    // Assign the value to the input field
    document.getElementById('porc_acierto_1_5').value                         = porcentaje_acierto_1_5;
    document.getElementById('porc_acierto_1_5_badge').innerText               = porcentaje_acierto_1_5 + '%';
    document.getElementById('porc_acierto_home_marca').value                  = porcentaje_acierto_home_marca;
    document.getElementById('porc_acierto_home_marca_badge').innerText        = porcentaje_acierto_home_marca + '%';
    document.getElementById('porc_acierto_home_superior_1_5').value           = porcentaje_acierto_home_superior_1_5;
    document.getElementById('porc_acierto_home_superior_1_5_badge').innerText = porcentaje_acierto_home_superior_1_5 + '%';
    document.getElementById('porc_acierto_away_marca').value                  = porcentaje_acierto_away_marca;
    document.getElementById('porc_acierto_away_marca_badge').innerText        = porcentaje_acierto_away_marca + '%';
    document.getElementById('porc_acierto_away_superior_1_5').value           = porcentaje_acierto_away_superior_1_5;
    document.getElementById('porc_acierto_away_superior_1_5_badge').innerText = porcentaje_acierto_away_superior_1_5 + '%';
    document.getElementById('porc_acierto_ambos_marcan').value                = porcentaje_acierto_ambos_marcan;
    document.getElementById('porc_acierto_ambos_marcan_badge').innerText      = porcentaje_acierto_ambos_marcan + '%';
    document.getElementById('porc_acierto_corners_6_5').value                 = porcentaje_acierto_corners_6_5;
    document.getElementById('porc_acierto_corners_6_5_badge').innerText       = porcentaje_acierto_corners_6_5 + '%';
    document.getElementById('porc_acierto_corners_7_5').value                 = porcentaje_acierto_corners_7_5;
    document.getElementById('porc_acierto_corners_7_5_badge').innerText       = porcentaje_acierto_corners_7_5 + '%';
    document.getElementById('porc_acierto_corners_8_5').value                 = porcentaje_acierto_corners_8_5;
    document.getElementById('porc_acierto_corners_8_5_badge').innerText       = porcentaje_acierto_corners_8_5 + '%';
    document.getElementById('porc_acierto_corners_9_5').value                 = porcentaje_acierto_corners_9_5;
    document.getElementById('porc_acierto_corners_9_5_badge').innerText       = porcentaje_acierto_corners_9_5 + '%';
    document.getElementById('porc_acierto_corners_10_5').value                = porcentaje_acierto_corners_10_5;
    document.getElementById('porc_acierto_corners_10_5_badge').innerText      = porcentaje_acierto_corners_10_5 + '%';
    document.getElementById('porc_acierto_corners_11_5').value                = porcentaje_acierto_corners_11_5;
    document.getElementById('porc_acierto_corners_11_5_badge').innerText      = porcentaje_acierto_corners_11_5 + '%';
    document.getElementById('porc_acierto_corners_12_5').value                = porcentaje_acierto_corners_12_5;
    document.getElementById('porc_acierto_corners_12_5_badge').innerText      = porcentaje_acierto_corners_12_5 + '%';
    document.getElementById('porc_acierto_corners_13_5').value                = porcentaje_acierto_corners_13_5;
    document.getElementById('porc_acierto_corners_13_5_badge').innerText      = porcentaje_acierto_corners_13_5 + '%';

    // Corners Inferior calculations - using < (less than) instead of >= (greater than or equal to)
    let total_count_corners_inferior_6_5       = 0;
    let total_count_corners_inferior_7_5       = 0;
    let total_count_corners_inferior_8_5       = 0;
    let total_count_corners_inferior_9_5       = 0;
    let total_count_corners_inferior_10_5      = 0;
    let total_count_corners_inferior_11_5      = 0;
    let total_count_corners_inferior_12_5      = 0;
    let total_count_corners_inferior_13_5      = 0;
    let total_criterios_corners_inferior_6_5   = 12;
    let total_criterios_corners_inferior_7_5   = 12;
    let total_criterios_corners_inferior_8_5   = 12;
    let total_criterios_corners_inferior_9_5   = 12;
    let total_criterios_corners_inferior_10_5  = 12;
    let total_criterios_corners_inferior_11_5  = 12;
    let total_criterios_corners_inferior_12_5  = 12;
    let total_criterios_corners_inferior_13_5  = 12;

    // Corners Inferior 6.5 calculations
    document.querySelectorAll('.value_porc_corners_inferior_6_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) { // Keep same as superior: >= min_porc_viable
            cell.classList.add('text-success'); // Add the class if the value is greater than or equal to min_porc_viable
            total_count_corners_inferior_6_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_inferior_6_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value < 6.0) { // Fixed: 6.5 - 0.5 = 6.0 (not 6.5 + 0.5 = 7.0)
            cell.classList.add('text-success'); // Add the class if the value is less than 6.0
            total_count_corners_inferior_6_5++; // Increment the total counter
        }
    });

    // Corners Inferior 7.5 calculations
    document.querySelectorAll('.value_porc_corners_inferior_7_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) { // Keep same as superior: >= min_porc_viable
            cell.classList.add('text-success'); // Add the class if the value is greater than or equal to min_porc_viable
            total_count_corners_inferior_7_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_inferior_7_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value < 7.0) { // Fixed: 7.5 - 0.5 = 7.0 (not 7.5 + 0.5 = 8.0)
            cell.classList.add('text-success'); // Add the class if the value is less than 7.0
            total_count_corners_inferior_7_5++; // Increment the total counter
        }
    });

    // Corners Inferior 8.5 calculations
    document.querySelectorAll('.value_porc_corners_inferior_8_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) { // Keep same as superior: >= min_porc_viable
            cell.classList.add('text-success'); // Add the class if the value is greater than or equal to min_porc_viable
            total_count_corners_inferior_8_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_inferior_8_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value < 8.0) { // Fixed: 8.5 - 0.5 = 8.0 (not 8.5 + 0.5 = 9.0)
            cell.classList.add('text-success'); // Add the class if the value is less than 8.0
            total_count_corners_inferior_8_5++; // Increment the total counter
        }
    });

    // Corners Inferior 9.5 calculations
    document.querySelectorAll('.value_porc_corners_inferior_9_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) { // Keep same as superior: >= min_porc_viable
            cell.classList.add('text-success'); // Add the class if the value is greater than or equal to min_porc_viable
            total_count_corners_inferior_9_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_inferior_9_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value < 9.0) { // Fixed: 9.5 - 0.5 = 9.0 (not 9.5 + 0.5 = 10.0)
            cell.classList.add('text-success'); // Add the class if the value is less than 9.0
            total_count_corners_inferior_9_5++; // Increment the total counter
        }
    });

    // Corners Inferior 10.5 calculations
    document.querySelectorAll('.value_porc_corners_inferior_10_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) { // Keep same as superior: >= min_porc_viable
            cell.classList.add('text-success'); // Add the class if the value is greater than or equal to min_porc_viable
            total_count_corners_inferior_10_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_inferior_10_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value < 10.0) { // Fixed: 10.5 - 0.5 = 10.0 (not 10.5 + 0.5 = 11.0)
            cell.classList.add('text-success'); // Add the class if the value is less than 10.0
            total_count_corners_inferior_10_5++; // Increment the total counter
        }
    });

    // Corners Inferior 11.5 calculations
    document.querySelectorAll('.value_porc_corners_inferior_11_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) { // Keep same as superior: >= min_porc_viable
            cell.classList.add('text-success'); // Add the class if the value is greater than or equal to min_porc_viable
            total_count_corners_inferior_11_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_inferior_11_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value < 11.0) { // Fixed: 11.5 - 0.5 = 11.0 (not 11.5 + 0.5 = 12.0)
            cell.classList.add('text-success'); // Add the class if the value is less than 11.0
            total_count_corners_inferior_11_5++; // Increment the total counter
        }
    });

    // Corners Inferior 12.5 calculations
    document.querySelectorAll('.value_porc_corners_inferior_12_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) { // Keep same as superior: >= min_porc_viable
            cell.classList.add('text-success'); // Add the class if the value is greater than or equal to min_porc_viable
            total_count_corners_inferior_12_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_inferior_12_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value < 12.0) { // Fixed: 12.5 - 0.5 = 12.0 (not 12.5 + 0.5 = 13.0)
            cell.classList.add('text-success'); // Add the class if the value is less than 12.0
            total_count_corners_inferior_12_5++; // Increment the total counter
        }
    });

    // Corners Inferior 13.5 calculations
    document.querySelectorAll('.value_porc_corners_inferior_13_5').forEach((cell) => {
        const value = parseInt(cell.textContent.replace('%', ''), 10); // Remove % and convert to number

        if (value >= <?php echo $min_porc_viable; ?>) { // Keep same as superior: >= min_porc_viable
            cell.classList.add('text-success'); // Add the class if the value is greater than or equal to min_porc_viable
            total_count_corners_inferior_13_5++; // Increment the total counter
        }
    });
    document.querySelectorAll('.value_num_corners_inferior_13_5').forEach((cell) => {
        const value = parseFloat(cell.textContent); // Convert to decimal number

        if (value < 13.0) { // Fixed: 13.5 - 0.5 = 13.0 (not 13.5 + 0.5 = 14.0)
            cell.classList.add('text-success'); // Add the class if the value is less than 13.0
            total_count_corners_inferior_13_5++; // Increment the total counter
        }
    });

    // Calculate percentages for inferior corners
    let porcentaje_acierto_corners_inferior_6_5       = Math.round((total_count_corners_inferior_6_5 * 100) / total_criterios_corners_inferior_6_5);
    let porcentaje_acierto_corners_inferior_7_5       = Math.round((total_count_corners_inferior_7_5 * 100) / total_criterios_corners_inferior_7_5);
    let porcentaje_acierto_corners_inferior_8_5       = Math.round((total_count_corners_inferior_8_5 * 100) / total_criterios_corners_inferior_8_5);
    let porcentaje_acierto_corners_inferior_9_5       = Math.round((total_count_corners_inferior_9_5 * 100) / total_criterios_corners_inferior_9_5);
    let porcentaje_acierto_corners_inferior_10_5      = Math.round((total_count_corners_inferior_10_5 * 100) / total_criterios_corners_inferior_10_5);
    let porcentaje_acierto_corners_inferior_11_5      = Math.round((total_count_corners_inferior_11_5 * 100) / total_criterios_corners_inferior_11_5);
    let porcentaje_acierto_corners_inferior_12_5      = Math.round((total_count_corners_inferior_12_5 * 100) / total_criterios_corners_inferior_12_5);
    let porcentaje_acierto_corners_inferior_13_5      = Math.round((total_count_corners_inferior_13_5 * 100) / total_criterios_corners_inferior_13_5);

    // Assign values to input fields and badges for inferior corners
    document.getElementById('porc_acierto_corners_inferior_6_5').value                 = porcentaje_acierto_corners_inferior_6_5;
    document.getElementById('porc_acierto_corners_inferior_6_5_badge').innerText       = porcentaje_acierto_corners_inferior_6_5 + '%';
    document.getElementById('porc_acierto_corners_inferior_7_5').value                 = porcentaje_acierto_corners_inferior_7_5;
    document.getElementById('porc_acierto_corners_inferior_7_5_badge').innerText       = porcentaje_acierto_corners_inferior_7_5 + '%';
    document.getElementById('porc_acierto_corners_inferior_8_5').value                 = porcentaje_acierto_corners_inferior_8_5;
    document.getElementById('porc_acierto_corners_inferior_8_5_badge').innerText       = porcentaje_acierto_corners_inferior_8_5 + '%';
    document.getElementById('porc_acierto_corners_inferior_9_5').value                 = porcentaje_acierto_corners_inferior_9_5;
    document.getElementById('porc_acierto_corners_inferior_9_5_badge').innerText       = porcentaje_acierto_corners_inferior_9_5 + '%';
    document.getElementById('porc_acierto_corners_inferior_10_5').value                = porcentaje_acierto_corners_inferior_10_5;
    document.getElementById('porc_acierto_corners_inferior_10_5_badge').innerText      = porcentaje_acierto_corners_inferior_10_5 + '%';
    document.getElementById('porc_acierto_corners_inferior_11_5').value                = porcentaje_acierto_corners_inferior_11_5;
    document.getElementById('porc_acierto_corners_inferior_11_5_badge').innerText      = porcentaje_acierto_corners_inferior_11_5 + '%';
    document.getElementById('porc_acierto_corners_inferior_12_5').value                = porcentaje_acierto_corners_inferior_12_5;
    document.getElementById('porc_acierto_corners_inferior_12_5_badge').innerText      = porcentaje_acierto_corners_inferior_12_5 + '%';
    document.getElementById('porc_acierto_corners_inferior_13_5').value                = porcentaje_acierto_corners_inferior_13_5;
    document.getElementById('porc_acierto_corners_inferior_13_5_badge').innerText      = porcentaje_acierto_corners_inferior_13_5 + '%';

    // Apply conditional badge colors (>60% becomes green)
    function applyConditionalBadgeColors() {
        const ids = [
            'porc_acierto_1_5_badge',
            'porc_acierto_home_marca_badge',
            'porc_acierto_home_superior_1_5_badge',
            'porc_acierto_away_marca_badge',
            'porc_acierto_away_superior_1_5_badge',
            'porc_acierto_ambos_marcan_badge',
            'porc_acierto_corners_6_5_badge',
            'porc_acierto_corners_7_5_badge',
            'porc_acierto_corners_8_5_badge',
            'porc_acierto_corners_9_5_badge',
            'porc_acierto_corners_10_5_badge',
            'porc_acierto_corners_11_5_badge',
            'porc_acierto_corners_12_5_badge',
            'porc_acierto_corners_13_5_badge',
            'porc_acierto_corners_inferior_6_5_badge',
            'porc_acierto_corners_inferior_7_5_badge',
            'porc_acierto_corners_inferior_8_5_badge',
            'porc_acierto_corners_inferior_9_5_badge',
            'porc_acierto_corners_inferior_10_5_badge',
            'porc_acierto_corners_inferior_11_5_badge',
            'porc_acierto_corners_inferior_12_5_badge',
            'porc_acierto_corners_inferior_13_5_badge'
        ];
        ids.forEach(id => {
            const el = document.getElementById(id);
            if (!el) return;
            const txt = (el.innerText || el.textContent || '').trim();
            const m = txt.match(/(\d+)/);
            const val = m ? parseInt(m[1], 10) : NaN;
            el.classList.remove('bg-success', 'bg-primary');
            if (!isNaN(val) && val > 60) {
                el.classList.add('bg-success');
            } else {
                el.classList.add('bg-primary');
            }
        });
    }
    // Run once after values set
    applyConditionalBadgeColors();
</script>
<?php #endregion JS colorear un TD si el contenido cumple una condicion y calcular % acierto ?>

<?php #region region JS upload modal ?>
<script type="text/javascript">
    function openUploadModal(paisId, paisNombre, season) {
        // Set hidden field values
        document.getElementById('pais_upload_id').value = paisId;

        // Set display values
        document.getElementById('torneo_display').textContent = paisNombre;
        document.getElementById('season_upload').value = season;

        // Clear file input
        document.getElementById('archivocsv_modal').value = '';

        // Show modal
        var modal = new bootstrap.Modal(document.getElementById('uploadTorneoModal'));
        modal.show();
    }
</script>
<?php #endregion JS upload modal ?>

<?php #region region JS countdown ?>
<script type="text/javascript">
    function updateCountdown() {
        <?php if (!empty($cur_partido->fecha) && !empty($cur_partido->horamilitar)): ?>
            // Create the target date using Bogotá timezone
            const targetDateStr = '<?php echo $cur_partido->fecha . ' ' . $cur_partido->horamilitar; ?>';
            const targetDate = new Date(targetDateStr);

            // Get current time in Bogotá timezone
            const now = new Date();
            const bogotaOffset = -5 * 60; // Bogotá is UTC-5
            const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
            const bogotaTime = new Date(utc + (bogotaOffset * 60000));

            // Calculate the difference
            const timeDiff = targetDate.getTime() - bogotaTime.getTime();

            if (timeDiff > 0) {
                // Calculate days, hours, and minutes
                const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

                // Format countdown text
                let countdownText = '';
                if (days > 0) {
                    countdownText += days + ' day' + (days !== 1 ? 's' : '') + ', ';
                }
                if (hours > 0 || days > 0) {
                    countdownText += hours + ' hour' + (hours !== 1 ? 's' : '') + ', ';
                }
                countdownText += minutes + ' minute' + (minutes !== 1 ? 's' : '');

                // Show countdown
                document.getElementById('countdown_text').textContent = countdownText;
                document.getElementById('countdown_container').style.display = 'inline-block';
                document.getElementById('match_status_container').style.display = 'none';
            } else {
                // Hide countdown if date is in the past
                document.getElementById('countdown_container').style.display = 'none';

                // Show match status based on time elapsed
                const minutesElapsed = Math.abs(timeDiff) / (1000 * 60);
                const matchStatusBadge = document.getElementById('match_status_badge');
                const matchStatusText = document.getElementById('match_status_text');

                if (minutesElapsed <= 120) { // 0-120 minutes = Started
                    matchStatusBadge.className = 'badge bg-warning text-dark fs-10px';
                    matchStatusText.innerHTML = '<i class="fas fa-play me-1"></i>Comenzado';
                } else { // More than 120 minutes = Finished
                    matchStatusBadge.className = 'badge bg-success fs-10px';
                    matchStatusText.innerHTML = '<i class="fas fa-flag-checkered me-1"></i>Terminado';
                }

                document.getElementById('match_status_container').style.display = 'inline-block';
            }
        <?php endif; ?>
    }

    // Update countdown immediately and then every minute
    updateCountdown();
    setInterval(updateCountdown, 60000); // Update every minute
</script>
<?php #endregion JS countdown ?>

<?php #region region JS grouped controls ?>
<script src="<?php echo RUTA ?>resources/js/grouped_controls.js"></script>
<?php #endregion JS grouped controls ?>

<?php #region region JS AJAX betting functionality ?>
<script type="text/javascript">
    // Pass current partido's country/tournament info to JavaScript for modal pre-selection
    window.currentPartidoCountryId = '<?php echo !empty($cur_partido) && !empty($cur_partido->pais_torneo->id) ? limpiar_datos($cur_partido->pais_torneo->id) : ''; ?>';
    window.currentPartidoCountryName = '<?php echo !empty($cur_partido) ? limpiar_datos($cur_partido->pais) : ''; ?>';

    // Function to extract criteria data from Home Superior 0.5 tab
    function extractHomeSuperior05Criteria() {
        const criteria = [];

        // Get the table from the Home Superior 0.5 tab (tab-3)
        const homeTab = document.getElementById('default-tab-3');
        if (!homeTab) {
            console.error('Home Superior 0.5 tab not found');
            return criteria;
        }

        const table = homeTab.querySelector('table');
        if (!table) {
            console.error('Table not found in Home Superior 0.5 tab');
            return criteria;
        }

        // Get header names from the table
        const headers = table.querySelectorAll('thead tr:last-child th');
        const dataRow = table.querySelector('tbody tr');

        if (!dataRow) {
            console.error('Data row not found in table');
            return criteria;
        }

        const dataCells = dataRow.querySelectorAll('td');

        // Map headers to data cells
        headers.forEach((header, index) => {
            if (index < dataCells.length) {
                const headerText = header.textContent.trim().replace(/\s+/g, ' ');
                const cellText = dataCells[index].textContent.trim();

                // Skip empty headers or cells
                if (headerText && cellText) {
                    // Extract numeric value (remove % sign if present)
                    let value = cellText.replace('%', '');

                    // Convert to number if possible
                    if (!isNaN(value) && value !== '') {
                        criteria.push({
                            nombre: headerText,
                            valor: parseFloat(value)
                        });
                    }
                }
            }
        });

        return criteria;
    }

    // Generic function to extract criteria data from any tab
    function extractCriteriaByTabType(tabType) {
        const tabConfigs = {
            'total_superior_1_5': { tabId: 'default-tab-1' },
            'home_superior_0_5': { tabId: 'default-tab-3' },
            'home_superior_1_5': { tabId: 'default-tab-20' },
            'away_superior_0_5': { tabId: 'default-tab-4' },
            'away_superior_1_5': { tabId: 'default-tab-21' },
            'ambos_marcan': { tabId: 'default-tab-5' },
            'corner_6_5': { tabId: 'corners-tab-7' },
            'corner_7_5': { tabId: 'corners-tab-8' },
            'corner_8_5': { tabId: 'corners-tab-9' },
            'corner_9_5': { tabId: 'corners-tab-10' },
            'corner_10_5': { tabId: 'corners-tab-11' },
            'corner_11_5': { tabId: 'corners-tab-12' },
            'corner_12_5': { tabId: 'corners-tab-13' },
            'corner_13_5': { tabId: 'corners-tab-14' },
            'corner_inferior_6_5': { tabId: 'corners-inferior-tab-7' },
            'corner_inferior_7_5': { tabId: 'corners-inferior-tab-8' },
            'corner_inferior_8_5': { tabId: 'corners-inferior-tab-9' },
            'corner_inferior_9_5': { tabId: 'corners-inferior-tab-10' },
            'corner_inferior_10_5': { tabId: 'corners-inferior-tab-11' },
            'corner_inferior_11_5': { tabId: 'corners-inferior-tab-12' },
            'corner_inferior_12_5': { tabId: 'corners-inferior-tab-13' },
            'corner_inferior_13_5': { tabId: 'corners-inferior-tab-14' }
        };

        const config = tabConfigs[tabType];
        if (!config) {
            console.error('Unknown tab type:', tabType);
            return [];
        }

        const criteria = [];
        const tab = document.getElementById(config.tabId);
        if (!tab) {
            console.error('Tab not found:', config.tabId);
            return criteria;
        }

        const table = tab.querySelector('table');
        if (!table) {
            console.error('Table not found in tab:', config.tabId);
            return criteria;
        }

        // Get header names from the table
        const headers = table.querySelectorAll('thead tr:last-child th');
        const dataRow = table.querySelector('tbody tr');

        if (!dataRow) {
            console.error('Data row not found in table');
            return criteria;
        }

        const dataCells = dataRow.querySelectorAll('td');

        // Map headers to data cells
        headers.forEach((header, index) => {
            if (index < dataCells.length) {
                const headerText = header.textContent.trim().replace(/\s+/g, ' ');
                const cellText = dataCells[index].textContent.trim();

                // Skip empty headers and cells
                if (headerText && cellText) {
                    // Clean up percentage values
                    let cleanValue = cellText;
                    if (cellText.includes('%')) {
                        cleanValue = cellText.replace('%', '');
                    }

                    criteria.push({
                        nombre: headerText,
                        valor: cleanValue
                    });
                }
            }
        });

        return criteria;
    }

    // Function to update checkmark icon for a specific tab type
    function updateCheckmarkIcon(tabType, showCheckmark) {
        // Map tab types to their corresponding nav-link selectors
        const tabSelectors = {
            'total_superior_1_5': 'a[href="#default-tab-1"]',
            'home_superior_0_5': 'a[href="#default-tab-3"]',
            'home_superior_1_5': 'a[href="#default-tab-20"]',
            'away_superior_0_5': 'a[href="#default-tab-4"]',
            'away_superior_1_5': 'a[href="#default-tab-21"]',
            'ambos_marcan': 'a[href="#default-tab-5"]',
            'corner_6_5': 'a[href="#corners-tab-7"]',
            'corner_7_5': 'a[href="#corners-tab-8"]',
            'corner_8_5': 'a[href="#corners-tab-9"]',
            'corner_9_5': 'a[href="#corners-tab-10"]',
            'corner_10_5': 'a[href="#corners-tab-11"]',
            'corner_11_5': 'a[href="#corners-tab-12"]',
            'corner_12_5': 'a[href="#corners-tab-13"]',
            'corner_13_5': 'a[href="#corners-tab-14"]',
            'corner_inferior_6_5': 'a[href="#corners-inferior-tab-7"]',
            'corner_inferior_7_5': 'a[href="#corners-inferior-tab-8"]',
            'corner_inferior_8_5': 'a[href="#corners-inferior-tab-9"]',
            'corner_inferior_9_5': 'a[href="#corners-inferior-tab-10"]',
            'corner_inferior_10_5': 'a[href="#corners-inferior-tab-11"]',
            'corner_inferior_11_5': 'a[href="#corners-inferior-tab-12"]',
            'corner_inferior_12_5': 'a[href="#corners-inferior-tab-13"]',
            'corner_inferior_13_5': 'a[href="#corners-inferior-tab-14"]'
        };

        const selector = tabSelectors[tabType];
        if (!selector) {
            console.warn('No selector found for tab type:', tabType);
            return;
        }

        const navLink = document.querySelector(selector);
        if (!navLink) {
            console.warn('Nav link not found for selector:', selector);
            return;
        }

        // Remove existing checkmark icon if present
        const existingCheckmark = navLink.querySelector('.fa-check-circle');
        if (existingCheckmark) {
            existingCheckmark.remove();
        }

        // Add checkmark icon if showCheckmark is true
        if (showCheckmark) {
            const checkmarkIcon = document.createElement('i');
            checkmarkIcon.className = 'fas fa-check-circle text-info ms-1';
            checkmarkIcon.title = 'Bet already exists for this match and bet type';
            navLink.appendChild(checkmarkIcon);
        }
    }

    // Function to get badge ID based on tab type
    function getBadgeIdByTabType(tabType) {
        const badgeMapping = {
            'total_superior_1_5': 'porc_acierto_1_5_badge',
            'home_superior_0_5': 'porc_acierto_home_marca_badge',
            'home_superior_1_5': 'porc_acierto_home_superior_1_5_badge',
            'away_superior_0_5': 'porc_acierto_away_marca_badge',
            'away_superior_1_5': 'porc_acierto_away_superior_1_5_badge',
            'ambos_marcan': 'porc_acierto_ambos_marcan_badge',
            'corner_6_5': 'porc_acierto_corners_6_5_badge',
            'corner_7_5': 'porc_acierto_corners_7_5_badge',
            'corner_8_5': 'porc_acierto_corners_8_5_badge',
            'corner_9_5': 'porc_acierto_corners_9_5_badge',
            'corner_10_5': 'porc_acierto_corners_10_5_badge',
            'corner_11_5': 'porc_acierto_corners_11_5_badge',
            'corner_12_5': 'porc_acierto_corners_12_5_badge',
            'corner_13_5': 'porc_acierto_corners_13_5_badge',
            'corner_inferior_6_5': 'porc_acierto_corners_inferior_6_5_badge',
            'corner_inferior_7_5': 'porc_acierto_corners_inferior_7_5_badge',
            'corner_inferior_8_5': 'porc_acierto_corners_inferior_8_5_badge',
            'corner_inferior_9_5': 'porc_acierto_corners_inferior_9_5_badge',
            'corner_inferior_10_5': 'porc_acierto_corners_inferior_10_5_badge',
            'corner_inferior_11_5': 'porc_acierto_corners_inferior_11_5_badge',
            'corner_inferior_12_5': 'porc_acierto_corners_inferior_12_5_badge',
            'corner_inferior_13_5': 'porc_acierto_corners_inferior_13_5_badge'
        };
        return badgeMapping[tabType] || null;
    }

    // Function to extract percentage value from badge
    function extractPercentageFromBadge(tabType) {
        const badgeId = getBadgeIdByTabType(tabType);
        if (!badgeId) {
            console.warn('No badge ID found for tab type:', tabType);
            return 0;
        }

        const badgeElement = document.getElementById(badgeId);
        if (!badgeElement) {
            console.warn('Badge element not found:', badgeId);
            return 0;
        }

        const badgeText = (badgeElement.innerText || badgeElement.textContent || '').trim();
        const match = badgeText.match(/(\d+)/);
        const percentage = match ? parseInt(match[1], 10) : 0;


        return percentage;
    }

    // Function to determine if a criterion is met based on the same logic used for cell coloring
    function isCriterionMet(criterionName, value, tabType) {
        const numericValue = parseFloat(value);
        const minPorcViable = <?php echo $min_porc_viable; ?>; // Get the PHP variable

        // Handle percentage criteria (those that end with % or contain percentage values)
        if (criterionName.includes('@') || criterionName.includes('Home') || criterionName.includes('Away')) {
            // Check if this is a percentage value (typically >= 70%)
            if (numericValue >= minPorcViable && numericValue <= 100) {
                return 1; // Met
            }
        }

        // Handle numeric criteria based on criterion name and tab type
        const lowerName = criterionName.toLowerCase();

        // Goals-related criteria
        if (lowerName.includes('avg goals') || lowerName.includes('goals')) {
            if (tabType === 'home_superior_1_5' || tabType === 'away_superior_1_5') {
                return numericValue >= 2.50 ? 1 : 0;
            } else {
                return numericValue >= 1.50 ? 1 : 0;
            }
        }

        // Conceded goals criteria
        if (lowerName.includes('conceded')) {
            if (tabType === 'home_superior_1_5' || tabType === 'away_superior_1_5') {
                return numericValue >= 2.00 ? 1 : 0;
            } else {
                return numericValue >= 1.50 ? 1 : 0;
            }
        }

        // Corners criteria
        if (lowerName.includes('corners')) {
            // Extract corner threshold from tab type
            if (tabType.includes('corner_')) {
                const cornerMatch = tabType.match(/corner_(\d+)_5/);
                if (cornerMatch) {
                    const threshold = parseFloat(cornerMatch[1]) + 0.5;
                    return numericValue >= threshold ? 1 : 0;
                }
            }
            // Default corner threshold
            return numericValue >= 7.0 ? 1 : 0;
        }

        // Default: assume it's a percentage if value is <= 100, otherwise numeric with 1.5 threshold
        if (numericValue <= 100) {
            return numericValue >= minPorcViable ? 1 : 0;
        } else {
            return numericValue >= 1.50 ? 1 : 0;
        }
    }

    // Function to show betting modal with criteria preview
    window.showBettingModal = function(tabType, betTypeCode) {
        // Store current tab type and bet type code for later use
        window.currentBetTabType = tabType;
        window.currentBetTypeCode = betTypeCode;

        const criteria = extractCriteriaByTabType(tabType);

        if (criteria.length === 0) {
            alert('No se pudieron extraer los criterios de la tabla. Por favor, verifique que la pestaña esté cargada correctamente.');
            return;
        }

        // Update modal title
        const tabNames = {
            'total_superior_1_5': 'Total Superior 1.5',
            'home_superior_0_5': 'Home Superior 0.5',
            'home_superior_1_5': 'Home Superior 1.5',
            'away_superior_0_5': 'Away Superior 0.5',
            'away_superior_1_5': 'Away Superior 1.5',
            'ambos_marcan': 'Ambos Marcan',
            'corner_6_5': 'Corners Superior 6.5',
            'corner_7_5': 'Corners Superior 7.5',
            'corner_8_5': 'Corners Superior 8.5',
            'corner_9_5': 'Corners Superior 9.5',
            'corner_10_5': 'Corners Superior 10.5',
            'corner_11_5': 'Corners Superior 11.5',
            'corner_12_5': 'Corners Superior 12.5',
            'corner_13_5': 'Corners Superior 13.5',
            'corner_inferior_6_5': 'Corners Inferior 6.5',
            'corner_inferior_7_5': 'Corners Inferior 7.5',
            'corner_inferior_8_5': 'Corners Inferior 8.5',
            'corner_inferior_9_5': 'Corners Inferior 9.5',
            'corner_inferior_10_5': 'Corners Inferior 10.5',
            'corner_inferior_11_5': 'Corners Inferior 11.5',
            'corner_inferior_12_5': 'Corners Inferior 12.5',
            'corner_inferior_13_5': 'Corners Inferior 13.5'
        };
        document.getElementById('betting_tab_name').textContent = tabNames[tabType] || tabType;

        // Populate criteria preview
        const previewDiv = document.getElementById('criteria_preview');
        let previewHtml = '<div class="row">';

        criteria.forEach((criterio, index) => {
            previewHtml += `
                <div class="col-md-6 mb-2">
                    <strong>${criterio.nombre}:</strong> ${criterio.valor}${criterio.nombre.includes('%') ? '%' : ''}
                </div>
            `;
        });

        previewHtml += '</div>';
        previewDiv.innerHTML = previewHtml;

        // Clear previous cuota value
        document.getElementById('betting_cuota').value = '';

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('bettingModal'));
        modal.show();

        // Focus on cuota input
        setTimeout(() => {
            document.getElementById('betting_cuota').focus();
        }, 500);
    }

    // Function to confirm bet via AJAX
    window.confirmBet = function() {
        const cuota = document.getElementById('betting_cuota').value;
        const tabType = window.currentBetTabType;
        const betTypeCode = window.currentBetTypeCode;
        const criteria = extractCriteriaByTabType(tabType);

        // Validate cuota
        if (!cuota || isNaN(cuota) || parseFloat(cuota) <= 1.0) {
            alert('Por favor, ingrese una cuota válida mayor a 1.00');
            document.getElementById('betting_cuota').focus();
            return;
        }

        if (criteria.length === 0) {
            alert('No se pudieron extraer los criterios. Por favor, intente nuevamente.');
            return;
        }

        if (!tabType || !betTypeCode) {
            alert('Error: No se pudo determinar el tipo de apuesta. Por favor, intente nuevamente.');
            return;
        }

        // Add criterion fulfillment status to each criterion
        const criteriaWithFulfillment = criteria.map(criterio => {
            const isMet = isCriterionMet(criterio.nombre, criterio.valor, tabType);
            return {
                nombre: criterio.nombre,
                valor: criterio.valor,
                cumplido: isMet
            };
        });

        // Extract probability percentage from the corresponding badge
        const probabilidad = extractPercentageFromBadge(tabType);

        // Disable button to prevent double submission
        const confirmBtn = document.getElementById('btn_confirm_bet');
        const originalText = confirmBtn.innerHTML;
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Procesando...';

        // Prepare AJAX data based on tab type
        const ajaxData = {
            ajax: 1,
            cuota: cuota,
            criterios: criteriaWithFulfillment,
            bet_type_code: betTypeCode,
            probabilidad: probabilidad
        };

        // Set the appropriate AJAX action based on tab type
        const ajaxActions = {
            'total_superior_1_5': 'ajax_add_bet_total_superior_1_5',
            'home_superior_0_5': 'ajax_add_bet_home_superior_0_5',
            'home_superior_1_5': 'ajax_add_bet_home_superior_1_5',
            'away_superior_0_5': 'ajax_add_bet_away_superior_0_5',
            'away_superior_1_5': 'ajax_add_bet_away_superior_1_5',
            'ambos_marcan': 'ajax_add_bet_ambos_marcan',
            'corner_6_5': 'ajax_add_bet_corner_6_5',
            'corner_7_5': 'ajax_add_bet_corner_7_5',
            'corner_8_5': 'ajax_add_bet_corner_8_5',
            'corner_9_5': 'ajax_add_bet_corner_9_5',
            'corner_10_5': 'ajax_add_bet_corner_10_5',
            'corner_11_5': 'ajax_add_bet_corner_11_5',
            'corner_12_5': 'ajax_add_bet_corner_12_5',
            'corner_13_5': 'ajax_add_bet_corner_13_5',
            'corner_inferior_6_5': 'ajax_add_bet_corner_inferior_6_5',
            'corner_inferior_7_5': 'ajax_add_bet_corner_inferior_7_5',
            'corner_inferior_8_5': 'ajax_add_bet_corner_inferior_8_5',
            'corner_inferior_9_5': 'ajax_add_bet_corner_inferior_9_5',
            'corner_inferior_10_5': 'ajax_add_bet_corner_inferior_10_5',
            'corner_inferior_11_5': 'ajax_add_bet_corner_inferior_11_5',
            'corner_inferior_12_5': 'ajax_add_bet_corner_inferior_12_5',
            'corner_inferior_13_5': 'ajax_add_bet_corner_inferior_13_5'
        };

        ajaxData[ajaxActions[tabType]] = 1;

        // Send AJAX request
        $.ajax({
            url: 'epartido_probabilidades',
            method: 'POST',
            dataType: 'json',
            data: ajaxData,
            success: function(response) {
                if (response.status === 'success') {
                    // Hide modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('bettingModal'));
                    modal.hide();

                    // Show success message with SweetAlert
                    swal({
                        title: '¡Éxito!',
                        text: '¡Apuesta agregada exitosamente!\n\nCuota: ' + cuota + '\nProbabilidad: ' + probabilidad + '%\nCriterios guardados: ' + response.data.criterios_count,
                        icon: 'success',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-success',
                                closeModal: true
                            }
                        }
                    });

                    // Update checkmark icon for this tab type
                    updateCheckmarkIcon(tabType, true);

                } else {
                    // Show error message with SweetAlert
                    swal({
                        title: 'Error',
                        text: response.message,
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-danger',
                                closeModal: true
                            }
                        }
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.error('Response:', xhr.responseText);
                alert('Error al procesar la solicitud. Por favor, intente nuevamente.');
            },
            complete: function() {
                // Re-enable button
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = originalText;
            }
        });
    }

    // Event listeners
    $(document).ready(function() {
        // Confirm bet button click
        $('#btn_confirm_bet').click(function() {
            window.confirmBet();
        });

        // Enter key on cuota input
        $('#betting_cuota').keypress(function(e) {
            if (e.which === 13) { // Enter key
                window.confirmBet();
            }
        });

        // Focus cuota input when modal is shown
        $('#bettingModal').on('shown.bs.modal', function() {
            $('#betting_cuota').focus();
        });
    });
</script>
<?php #endregion JS AJAX betting functionality ?>

<?php #region region JS Historical filtering functionality ?>
<script type="text/javascript">
$(document).ready(function() {
    // Store original data and current partido info
    const originalRows = [];
    const partidoHome = '<?php echo addslashes($cur_partido->home); ?>';
    const partidoAway = '<?php echo addslashes($cur_partido->away); ?>';

    // Store original table rows
    $('#historico-table tbody tr').each(function(index) {
        const row = $(this);
        const partidoInfoHome = row.find('td:nth-child(4)').text().trim(); // Home is 4th column
        const partidoInfoAway = row.find('td:nth-child(8)').text().trim(); // Away is 8th column

        originalRows.push({
            element: row.clone(),
            home: partidoInfoHome,
            away: partidoInfoAway,
            rowNumber: index + 1,
            separatorClass: row.hasClass('historical-separator-5') ? 'historical-separator-5' :
                (row.hasClass('historical-separator-10') ? 'historical-separator-10' : '')
        });
    });



    // Initialize with 'todos' filter to show all records
    filterHistorico('todos');

    // Filter function
    function filterHistorico(filterType) {
        let filteredRows = [];


        originalRows.forEach(function(rowData) {
            let showRow = false;

            // Normalize strings for comparison (trim and handle potential encoding issues)
            const normalizedRowHome = rowData.home.trim();
            const normalizedRowAway = rowData.away.trim();
            const normalizedPartidoHome = partidoHome.trim();
            const normalizedPartidoAway = partidoAway.trim();

            switch(filterType) {
                case 'todos':
                    showRow = true;
                    break;
                case 'home':
                    // Home: Show ALL matches played by the Home team (both when playing at home AND when playing away)
                    showRow = (normalizedRowHome === normalizedPartidoHome || normalizedRowAway === normalizedPartidoHome);
                    break;
                case 'home_h':
                    // Home @H: Show records where the Home team played at home (PartidoInfo.home = Partido.home)
                    showRow = (normalizedRowHome === normalizedPartidoHome);
                    break;
                case 'home_a':
                    // Home @A: Show records where the Home team played away (PartidoInfo.away = Partido.home)
                    showRow = (normalizedRowAway === normalizedPartidoHome);
                    break;
                case 'away':
                    // Away: Show ALL matches played by the Away team (both when playing at home AND when playing away)
                    showRow = (normalizedRowHome === normalizedPartidoAway || normalizedRowAway === normalizedPartidoAway);
                    break;
                case 'away_a':
                    // Away @A: Show records where the Away team played away (PartidoInfo.away = Partido.away)
                    showRow = (normalizedRowAway === normalizedPartidoAway);
                    break;
                case 'away_h':
                    // Away @H: Show records where the Away team played at home (PartidoInfo.home = Partido.away)
                    showRow = (normalizedRowHome === normalizedPartidoAway);
                    break;
            }

            if (showRow) {
                filteredRows.push(rowData);
            }
        });



        // Clear current table
        $('#historico-table tbody').empty();

        // Add filtered rows with correct numbering and separators
        filteredRows.forEach(function(rowData, index) {
            const newRow = rowData.element.clone();

            // Update row number
            newRow.find('td:first-child').text(index + 1);

            // Remove old separator classes
            newRow.removeClass('historical-separator-5 historical-separator-10');

            // Add separator classes at positions 5 and 10
            if (index + 1 === 5) {
                newRow.addClass('historical-separator-5');
            } else if (index + 1 === 10) {
                newRow.addClass('historical-separator-10');
            }

            $('#historico-table tbody').append(newRow);
        });

        // Update counter
        $('#historico-counter').text(filteredRows.length);
    }

    // Handle filter button clicks
    $('#historico-filters label').on('click', function() {
        const filterType = $(this).data('filter');

        // Update active state
        $('#historico-filters label').removeClass('active');
        $(this).addClass('active');

        // Apply filter
        filterHistorico(filterType);
    });

    // Excel export functionality
    $('#export-excel-btn').on('click', function() {
        exportToExcel();
    });

    function exportToExcel() {
        // Get current filter (fallback to 'todos' if none active)
        let activeFilter = $('#historico-filters label.active').data('filter');
        if (!activeFilter) { activeFilter = 'todos'; }

        // Show loading state
        $('#export-excel-btn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Exporting...');

        // Create form data for POST request
        const formData = new FormData();
        formData.append('id_partido', '<?php echo $cur_partido->id; ?>');
        formData.append('filter_type', activeFilter);
        formData.append('partido_home', partidoHome);
        formData.append('partido_away', partidoAway);

        // Create XMLHttpRequest for file download
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '<?php echo RUTA; ?>src/export_historico_excel.php', true);
        xhr.responseType = 'blob';
        // Optional timeout to avoid indefinite hanging
        xhr.timeout = 120000; // 120s

        xhr.onload = function() {
            try {
                if (xhr.status === 200) {
                    // Create download link with correct XLSX MIME and sanitized filename
                    const blob = new Blob([xhr.response], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    const unsafeName = 'historico_' + partidoHome + '_vs_' + partidoAway + '_' + activeFilter + '.xlsx';
                    const safeName = unsafeName.replace(/[\\/:*?"<>|]/g, '_');
                    link.download = safeName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);

                    // Show success message
                    toastr.success('Excel file downloaded successfully');
                } else {
                    // Attempt to read error message if provided
                    toastr.error('Error generating Excel file');
                    console.error('Export error:', xhr.status, xhr.statusText);
                }
            } catch (e) {
                console.error('Export exception:', e);
                toastr.error('Unexpected error during export');
            } finally {
                // Reset button state
                $('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
            }
        };

        xhr.onerror = function() {
            toastr.error('Network error occurred while exporting');
            $('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
        };

        xhr.onabort = function() {
            toastr.warning('Export aborted');
            $('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
        };

        xhr.ontimeout = function() {
            toastr.error('Export timed out');
            $('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
        };

        xhr.send(formData);
    }
});
</script>
<?php #endregion JS Historical filtering functionality ?>

<?php #region region JS Actualizar via API ?>
<script type="text/javascript">
function actualizarViaAPI(paisId, season, footyId, paisNombre) {
    // Show loading indicator
    const loadingHtml = '<div class="d-flex justify-content-center align-items-center" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 9999;"><div class="text-center text-white"><div class="spinner-border mb-3" role="status"></div><h5>Actualizando datos via API...</h5><p>Por favor espere, esto puede tomar varios minutos.</p></div></div>';
    $('body').append(loadingHtml);

    // Disable all buttons to prevent double-clicks
    $('button').prop('disabled', true);

    // Make AJAX call to process API data
    $.ajax({
        url: 'epartido_probabilidades',
        method: 'POST',
        dataType: 'json',
        data: {
            sub_actualizar_via_api: 1,
            pais_id: paisId,
            season: season,
            footy_id: footyId,
            id_partido: '<?php echo $id_partido; ?>'
        },
        success: function(response) {
            // Remove loading indicator
            $('.d-flex.justify-content-center').remove();
            $('button').prop('disabled', false);

            if (response.status === 'success') {
                // Show success message with SweetAlert
                swal({
                    title: '¡Éxito!',
                    text: response.message + '\n\nRegistros creados: ' + response.records_created,
                    icon: 'success',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn btn-success',
                            closeModal: true
                        }
                    }
                }).then(() => {
                    // Optionally refresh the page to show updated data
                    window.location.reload();
                });
            } else {
                // Show error message
                swal({
                    title: 'Error',
                    text: response.message || 'Error al procesar los datos de la API',
                    icon: 'error',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn btn-danger',
                            closeModal: true
                        }
                    }
                });
            }
        },
        error: function(xhr, status, error) {
            // Remove loading indicator
            $('.d-flex.justify-content-center').remove();
            $('button').prop('disabled', false);

            console.error('AJAX Error:', error);
            console.error('Response:', xhr.responseText);

            swal({
                title: 'Error',
                text: 'Error al procesar la solicitud. Por favor, intente nuevamente.',
                icon: 'error',
                buttons: {
                    confirm: {
                        text: 'Ok',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            });
        },
        timeout: 300000 // 5 minutes timeout
    });
}
</script>
<?php #endregion JS Actualizar via API ?>

<?php #region region JS Floating Team Badges ?>
<script>
// Floating team badges scroll behavior - same as scroll-to-top button
$(document).ready(function() {
    var floatingBadges = $('.floating-badge');

    $(window).scroll(function() {
        if ($(this).scrollTop() > 200) {
            floatingBadges.fadeIn();
        } else {
            floatingBadges.fadeOut();
        }
    });
});

// Toast notification function for floating badges
function copyTeamWithToast(controlId) {
    const element = document.getElementById(controlId);

    if (element) {
        let textToCopy = '';

        // Handle team names with 14-character limit (same as existing copyAndShowTooltip function)
        if (controlId === 'floating-home' || controlId === 'floating-away') {
            // Get the cleaned text content and limit to first 14 characters
            textToCopy = element.textContent.trim().substring(0, 14).trim();
        } else {
            // For other elements, use cleaned textContent
            textToCopy = element.textContent.trim();
        }

        // Copy the cleaned text to the clipboard
        navigator.clipboard.writeText(textToCopy);

        // Create toast notification
        const toast = document.createElement("div");
        toast.textContent = "Team name copied!";
        toast.classList.add("floating-badge-toast");
        document.body.appendChild(toast);

        // Show the toast
        setTimeout(() => {
            toast.classList.add("show");
        }, 10);

        // Hide and remove the toast after 2 seconds
        setTimeout(() => {
            toast.classList.add("hide");
            setTimeout(() => {
                toast.remove();
            }, 300); // Wait for transition to complete
        }, 2000);
    } else {
        console.error(`Element with ID "${controlId}" not found.`);
    }
}

// Toast notification function for header team names and other clickable elements
function copyWithToast(controlId) {
    const element = document.getElementById(controlId);

    if (element) {
        let textToCopy = '';
        let toastMessage = '';

        // Handle different types of elements
        if (controlId === 'home' || controlId === 'away') {
            // For team names, get the cleaned text content and limit to first 14 characters
            textToCopy = element.textContent.trim().substring(0, 14).trim();
            toastMessage = "Team name copied!";
        } else if (controlId === 'torneo') {
            // For tournament name
            textToCopy = element.textContent.trim();
            toastMessage = "Tournament copied!";
        } else if (controlId === 'fecha_hora') {
            // For date and time
            textToCopy = element.textContent.trim();
            toastMessage = "Date & time copied!";
        } else {
            // For other elements, use cleaned textContent
            textToCopy = element.textContent.trim();
            toastMessage = "Text copied!";
        }

        // Copy the cleaned text to the clipboard
        navigator.clipboard.writeText(textToCopy);

        // Create toast notification
        const toast = document.createElement("div");
        toast.textContent = toastMessage;
        toast.classList.add("floating-badge-toast");
        document.body.appendChild(toast);

        // Show the toast
        setTimeout(() => {
            toast.classList.add("show");
        }, 10);

        // Hide and remove the toast after 2 seconds
        setTimeout(() => {
            toast.classList.add("hide");
            setTimeout(() => {
                toast.remove();
            }, 300); // Wait for transition to complete
        }, 2000);
    } else {
        console.error(`Element with ID "${controlId}" not found.`);
    }
}
</script>
<?php #endregion JS Floating Team Badges ?>

<?php #region region JS Tournament Information ?>
<script type="text/javascript">
// Tournament information functionality
$(document).ready(function() {
    // Pass PHP data to JavaScript
    window.teamIds = {
        home: <?php echo json_encode($cur_partido->id_home ?? null); ?>,
        away: <?php echo json_encode($cur_partido->id_away ?? null); ?>
    };

    // Load tournament information when page is ready
    loadTournamentInformation();
});

function loadTournamentInformation() {
    // Check if we have valid team IDs
    if (!window.teamIds.home || !window.teamIds.away) {
        console.log('Team IDs not available, skipping tournament information load');
        return;
    }

    // Load tournaments for both teams
    loadTeamTournaments('home', window.teamIds.home);
    loadTeamTournaments('away', window.teamIds.away);
}

function loadTeamTournaments(teamType, teamId) {
    const badgeContainer = document.getElementById(`${teamType}-tournament-badges`);

    // Show loading indicator
    badgeContainer.innerHTML = '<div class="badge bg-secondary fs-15px"><i class="fas fa-spinner fa-spin me-1"></i>Loading...</div>';

    // Make AJAX call to PHP endpoint
    $.ajax({
        url: 'epartido_probabilidades',
        method: 'POST',
        dataType: 'json',
        data: {
            ajax_get_team_tournaments: 1,
            team_id: teamId
        },
        success: function(response) {
            if (response.status === 'success') {
                displayTournamentBadges(teamType, response.tournaments);
            } else {
                throw new Error(response.message || 'Unknown error');
            }
        },
        error: function(xhr, status, error) {
            console.error(`Error loading tournaments for ${teamType} team:`, error);
            console.error('Response:', xhr.responseText);
            displayErrorBadge(teamType);
        }
    });
}

function displayTournamentBadges(teamType, tournaments) {
    const badgeContainer = document.getElementById(`${teamType}-tournament-badges`);

    if (tournaments.length === 0) {
        badgeContainer.innerHTML = '<div class="badge bg-muted fs-15px">No tournaments</div>';
        return;
    }

    // Group tournaments by country name to remove duplicates
    const uniqueTournaments = {};
    tournaments.forEach(tournament => {
        if (!uniqueTournaments[tournament.country_name]) {
            uniqueTournaments[tournament.country_name] = {
                country_name: tournament.country_name,
                competition_ids: [tournament.competition_id]
            };
        } else {
            // Add competition ID if not already present
            if (!uniqueTournaments[tournament.country_name].competition_ids.includes(tournament.competition_id)) {
                uniqueTournaments[tournament.country_name].competition_ids.push(tournament.competition_id);
            }
        }
    });

    // Create badges from unique tournament data
    const badges = Object.values(uniqueTournaments).map(tournament => {
        const competitionIdsText = tournament.competition_ids.join(', ');
        return `<div class="badge bg-info fs-15px" title="Competition IDs: ${competitionIdsText}">${tournament.country_name}</div>`;
    });

    badgeContainer.innerHTML = badges.join('');
}

function displayErrorBadge(teamType) {
    const badgeContainer = document.getElementById(`${teamType}-tournament-badges`);
    badgeContainer.innerHTML = '<div class="badge bg-danger fs-15px" title="Failed to load tournament information">Error loading</div>';
}
</script>
<?php #endregion JS Tournament Information ?>

<script src="<?php echo RUTA ?>resources/js/epartido_probabilidades.js"></script>

<?php #endregion JS ?>

</body>
</html>